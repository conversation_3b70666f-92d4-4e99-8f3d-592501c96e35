<template>
  <div class="test-map-container">
    <h3>地图测试</h3>
    <div class="map-wrapper">
      <div id="test-map" style="height: 400px; width: 100%;"></div>
    </div>
    <div class="test-info">
      <p>测试坐标: 广州 (23.1181, 113.2539)</p>
      <button @click="initTestMap" class="btn btn-primary">初始化地图</button>
      <button @click="testAPI" class="btn btn-secondary ms-2">测试API</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';

const isLoading = ref(true);
let map = null;
let L = null;

const initTestMap = async () => {
  try {
    console.log('开始初始化测试地图...');
    
    // 动态导入 Leaflet
    L = await import('leaflet');
    
    // 确保容器存在
    const mapContainer = document.getElementById('test-map');
    if (!mapContainer) {
      console.error('地图容器不存在');
      return;
    }

    // 清除现有地图
    if (map) {
      map.remove();
    }

    // 创建地图
    map = L.map('test-map').setView([23.1181, 113.2539], 13);

    // 添加地图图层
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // 添加标记
    const marker = L.marker([23.1181, 113.2539]).addTo(map);
    marker.bindPopup('<b>测试位置</b><br>广州, 中国').openPopup();

    // 添加精度圆圈
    L.circle([23.1181, 113.2539], {
      color: '#007bff',
      fillColor: '#007bff',
      fillOpacity: 0.1,
      radius: 5000 // 5km
    }).addTo(map);

    console.log('测试地图初始化成功');
    isLoading.value = false;

  } catch (error) {
    console.error('测试地图初始化失败:', error);
    isLoading.value = false;
  }
};

const testAPI = async () => {
  try {
    console.log('测试API调用...');
    const response = await fetch('/api/ipapicom?ip=***************');
    const data = await response.json();
    console.log('API返回数据:', data);

    if (data.latitude && data.longitude) {
      console.log(`API返回坐标: ${data.latitude}, ${data.longitude}`);
      // 更新地图位置
      if (map) {
        map.setView([data.latitude, data.longitude], 13);
        L.marker([data.latitude, data.longitude]).addTo(map)
          .bindPopup(`<b>API位置</b><br>${data.city}, ${data.country_name}<br>坐标: ${data.latitude}, ${data.longitude}`)
          .openPopup();
      }
    } else {
      console.log('API未返回有效坐标');
    }
  } catch (error) {
    console.error('API测试失败:', error);
  }
};

onMounted(() => {
  nextTick(() => {
    initTestMap();
  });
});
</script>

<style scoped>
.test-map-container {
  margin: 20px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.map-wrapper {
  margin: 15px 0;
  border: 1px solid #ccc;
  border-radius: 4px;
  overflow: hidden;
}

.test-info {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.test-info p {
  margin: 0 0 10px 0;
  font-weight: bold;
}
</style>
