// 合并的IP服务API
// import { refererMiddleware } from '../middleware/referer-middleware.js';

// 简化的referer检查中间件
const refererMiddleware = (req, res, next) => {
  // 在Vercel环境中简化referer检查
  next();
};

// IP-SB API
const ipSbHandler = async (req, res) => {
  try {
    const { ip } = req.query;

    // 如果没有提供IP，使用客户端IP
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    // 使用您的自定义API
    const response = await fetch(`https://myip.wobys.dpdns.org/api/ipsb?ip=${encodeURIComponent(targetIP)}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error fetching from IP.SB:', error);
    res.status(500).json({ error: 'Failed to fetch data from IP.SB' });
  }
};

// IP2Location.io API
const ip2locationHandler = async (req, res) => {
  try {
    const { ip } = req.query;

    // 如果没有提供IP，使用客户端IP
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    // 使用您的自定义API
    const response = await fetch(`https://myip.wobys.dpdns.org/api/ip2location?ip=${encodeURIComponent(targetIP)}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error fetching from IP2Location.io:', error);
    res.status(500).json({ error: 'Failed to fetch data from IP2Location.io' });
  }
};

// IPAPI.com API
const ipapiComHandler = async (req, res) => {
  try {
    const { ip } = req.query;
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    const response = await fetch(`https://myip.wobys.dpdns.org/api/ipapicom?ip=${encodeURIComponent(targetIP)}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error fetching from IPAPI.com:', error);
    res.status(500).json({ error: 'Failed to fetch data from IPAPI.com' });
  }
};

// IPAPI.is API
const ipapiIsHandler = async (req, res) => {
  try {
    const { ip } = req.query;
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    const response = await fetch(`https://myip.wobys.dpdns.org/api/ipapiis?ip=${encodeURIComponent(targetIP)}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error fetching from IPAPI.is:', error);
    res.status(500).json({ error: 'Failed to fetch data from IPAPI.is' });
  }
};

// IPCheck.ing API
const ipcheckHandler = async (req, res) => {
  try {
    const { ip } = req.query;
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    const response = await fetch(`https://myip.wobys.dpdns.org/api/ipchecking?ip=${encodeURIComponent(targetIP)}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error fetching from IPCheck.ing:', error);
    res.status(500).json({ error: 'Failed to fetch data from IPCheck.ing' });
  }
};

// IPInfo.io API
const ipinfoHandler = async (req, res) => {
  try {
    const { ip } = req.query;
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    const response = await fetch(`https://myip.wobys.dpdns.org/api/ipinfo?ip=${encodeURIComponent(targetIP)}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error fetching from IPInfo.io:', error);
    res.status(500).json({ error: 'Failed to fetch data from IPInfo.io' });
  }
};

// MaxMind API
const maxmindHandler = async (req, res) => {
  try {
    const { ip } = req.query;
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    const response = await fetch(`https://myip.wobys.dpdns.org/api/maxmind?ip=${encodeURIComponent(targetIP)}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error fetching from MaxMind:', error);
    res.status(500).json({ error: 'Failed to fetch data from MaxMind' });
  }
};

// 美团IP API
const meituanHandler = async (req, res) => {
  try {
    const { ip } = req.query;

    // 如果没有提供IP，使用客户端IP
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    // 使用您的自定义API
    const response = await fetch(`https://myip.wobys.dpdns.org/api/meituan-ip?ip=${encodeURIComponent(targetIP)}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error fetching from Meituan IP:', error);
    res.status(500).json({ error: 'Failed to fetch data from Meituan IP' });
  }
};

// IPAPI.co API
const ipapiCoHandler = async (req, res) => {
  try {
    const { ip } = req.query;

    // 如果没有提供IP，使用客户端IP
    const targetIP = ip || req.headers['cf-connecting-ip'] ||
                     req.headers['x-real-ip'] ||
                     req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                     req.connection?.remoteAddress;

    if (!targetIP) {
      return res.status(400).json({ error: 'No IP address provided' });
    }

    // 使用您的自定义API
    const response = await fetch(`https://myip.wobys.dpdns.org/api/ipapico?ip=${encodeURIComponent(targetIP)}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error fetching from IPAPI.co:', error);
    res.status(500).json({ error: 'Failed to fetch data from IPAPI.co' });
  }
};

// 主处理函数
export default async function handler(req, res) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // 处理OPTIONS请求
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // 应用referer中间件
  refererMiddleware(req, res, async () => {
    const { service } = req.query;
    
    switch (service) {
      case 'ip-sb':
        return ipSbHandler(req, res);
      case 'ip2location':
        return ip2locationHandler(req, res);
      case 'ipapi-com':
        return ipapiComHandler(req, res);
      case 'ipapi-is':
        return ipapiIsHandler(req, res);
      case 'ipcheck':
        return ipcheckHandler(req, res);
      case 'ipinfo':
        return ipinfoHandler(req, res);
      case 'maxmind':
        return maxmindHandler(req, res);
      case 'meituan':
        return meituanHandler(req, res);
      case 'ipapi-co':
        return ipapiCoHandler(req, res);
      default:
        res.status(400).json({ error: 'Invalid service parameter' });
    }
  });
}
