<template>
  <div class="ip-location-map">
    <div class="map-header">
      <h3>IP位置地图</h3>
      <div class="map-info">
        <span v-if="loading">正在加载地图...</span>
        <span v-else-if="error" class="error">{{ error }}</span>
        <span v-else-if="location">{{ location.city }}, {{ location.region }}, {{ location.country }}</span>
      </div>
    </div>
    
    <div class="map-container">
      <div id="map" ref="mapContainer" class="map"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'IPLocationMap',
  props: {
    ipData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      map: null,
      marker: null,
      loading: true,
      error: null,
      location: null
    }
  },
  watch: {
    ipData: {
      handler(newData) {
        if (newData && newData.latitude && newData.longitude) {
          this.location = {
            city: newData.city || '未知',
            region: newData.region || '未知',
            country: newData.country_name || newData.country || '未知'
          }
          this.initMap(newData.latitude, newData.longitude)
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.loadLeaflet()
  },
  beforeUnmount() {
    if (this.map) {
      this.map.remove()
    }
  },
  methods: {
    async loadLeaflet() {
      try {
        // 动态加载 Leaflet CSS
        if (!document.querySelector('link[href*="leaflet"]')) {
          const link = document.createElement('link')
          link.rel = 'stylesheet'
          link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css'
          document.head.appendChild(link)
        }

        // 动态加载 Leaflet JS
        if (!window.L) {
          await new Promise((resolve, reject) => {
            const script = document.createElement('script')
            script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js'
            script.onload = resolve
            script.onerror = reject
            document.head.appendChild(script)
          })
        }

        this.loading = false
        
        // 如果已经有IP数据，立即初始化地图
        if (this.ipData && this.ipData.latitude && this.ipData.longitude) {
          this.initMap(this.ipData.latitude, this.ipData.longitude)
        }
      } catch (err) {
        console.error('Failed to load Leaflet:', err)
        this.error = '地图加载失败'
        this.loading = false
      }
    },

    initMap(lat, lng) {
      if (!window.L || this.loading) return

      try {
        // 移除现有地图
        if (this.map) {
          this.map.remove()
        }

        // 创建地图
        this.map = window.L.map(this.$refs.mapContainer).setView([lat, lng], 10)

        // 添加瓦片图层
        window.L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '© OpenStreetMap contributors',
          maxZoom: 18
        }).addTo(this.map)

        // 添加标记
        if (this.marker) {
          this.map.removeLayer(this.marker)
        }
        
        this.marker = window.L.marker([lat, lng])
          .addTo(this.map)
          .bindPopup(`
            <div>
              <strong>IP位置</strong><br>
              ${this.location.city}, ${this.location.region}<br>
              ${this.location.country}<br>
              <small>纬度: ${lat.toFixed(4)}, 经度: ${lng.toFixed(4)}</small>
            </div>
          `)
          .openPopup()

        this.error = null
      } catch (err) {
        console.error('Failed to initialize map:', err)
        this.error = '地图初始化失败'
      }
    }
  }
}
</script>

<style scoped>
.ip-location-map {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.map-header h3 {
  margin: 0;
  color: #fff;
  font-size: 18px;
}

.map-info {
  color: #ccc;
  font-size: 14px;
}

.map-info .error {
  color: #ff6b6b;
}

.map-container {
  height: 300px;
  border-radius: 8px;
  overflow: hidden;
  background: #f0f0f0;
}

.map {
  width: 100%;
  height: 100%;
}

/* 确保地图控件可见 */
:deep(.leaflet-control-zoom) {
  border: none !important;
}

:deep(.leaflet-control-zoom a) {
  background-color: rgba(255, 255, 255, 0.9) !important;
  color: #333 !important;
}

:deep(.leaflet-popup-content-wrapper) {
  background: rgba(255, 255, 255, 0.95) !important;
  border-radius: 8px !important;
}
</style>
