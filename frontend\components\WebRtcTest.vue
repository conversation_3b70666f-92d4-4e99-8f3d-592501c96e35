<template>
  <!-- WebRTC Test -->
  <div class="webrtc-test-section mb-4">
    <div class="jn-title2">
      <h2 id="WebRTC" :class="{ 'mobile-h2': isMobile }">🚥 {{ t('webrtc.Title') }}</h2>
      <button @click="checkAllWebRTC(true)" :class="['btn', isDarkMode ? 'btn-dark dark-mode-refresh' : 'btn-light']"
        aria-label="Refresh WebRTC Test" v-tooltip="t('Tooltips.RefreshWebRTC')">
        <i class="bi" :class="[isStarted ? 'bi-arrow-clockwise' : 'bi-caret-right-fill']"></i>
      </button>
    </div>
    <div class="text-secondary">
      <p>{{ t('webrtc.Note') }}</p>
    </div>
    <div class="row">
      <div v-for="stun in stunServers" :key="stun.id" class="col-lg-3 col-md-6 col-12 mb-4">
        <div class="card jn-card keyboard-shortcut-card"
          :class="{ 'dark-mode dark-mode-border': isDarkMode, 'jn-hover-card': !isMobile }">
          <div class="card-body">
            <p class="card-title jn-con-title"><i class="bi bi-sign-merge-left-fill"></i> {{ stun.name }}</p>
            <p class="card-text text-secondary" style="font-size: 10pt;"><i class="bi bi-hdd-network-fill"></i> {{ stun.url }}</p>
            <p class="card-text" :class="{
              'text-info': stun.ip === t('webrtc.StatusWait'),
              'text-success': stun.ip.includes('.') || stun.ip.includes(':'),
              'text-danger': stun.ip === t('webrtc.StatusError')
            }">
              <i class="bi"
                :class="[stun.ip === t('webrtc.StatusWait') ? 'bi-hourglass-split' : 'bi-pc-display-horizontal']"></i>
              <span :class="{ 'jn-ip-font': stun.ip.length > 32 }"> {{ stun.ip }}
              </span>
            </p>
            <div v-if="stun.natType" class="alert d-flex flex-column" :class="{
              'alert-info': stun.natType === t('webrtc.StatusWait'),
              'alert-success': stun.natType !== t('webrtc.StatusWait'),
            }" :data-bs-theme="isDarkMode ? 'dark' : ''">
              <span>
                <i class="bi"
                  :class="[stun.natType === t('webrtc.StatusWait') ? 'bi-hourglass-split' : ' bi-controller']"></i> NAT:
                {{ stun.natType }}
              </span>

              <span class="mt-2">
                <i class="bi"
                  :class="[stun.country === t('webrtc.StatusWait') || stun.country === t('webrtc.StatusError') ? 'bi-hourglass-split' : 'bi-geo-alt-fill']"></i>
                {{ t('ipInfos.Country') }}: <span :class="[ stun.country !== t('webrtc.StatusWait') ? 'fw-bold':'']">
                {{ stun.country }}&nbsp;</span>
                <span v-show="stun.country_code" :class="'jn-fl fi fi-' + stun.country_code"></span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, reactive, watch } from 'vue';
  import { useMainStore } from '@/store';
  import { useI18n } from 'vue-i18n';
  import { trackEvent } from '@/utils/use-analytics';
  import { transformDataFromIPapi } from '@/utils/transform-ip-data.js';
  import getCountryName from '@/utils/country-name.js';
  import { authenticatedFetch } from '@/utils/authenticated-fetch.js';

  const { t } = useI18n();

  const store = useMainStore();
  const isDarkMode = computed(() => store.isDarkMode);
  const isMobile = computed(() => store.isMobile);
  const lang = computed(() => store.lang);

  const isStarted = ref(false);
  const IPArray = ref([]);
  const stunServers = reactive([
    {
      id: "google1",
      name: "Google STUN 1",
      url: "stun.l.google.com:19302",
      ip: t('webrtc.StatusWait'),
      natType: t('webrtc.StatusWait'),
      country: t('webrtc.StatusWait'),
      country_code: '',
    },
    {
      id: "google2",
      name: "Google STUN 2",
      url: "stun1.l.google.com:19302",
      ip: t('webrtc.StatusWait'),
      natType: t('webrtc.StatusWait'),
      country: t('webrtc.StatusWait'),
      country_code: '',
    },
    {
      id: "google3",
      name: "Google STUN 3",
      url: "stun2.l.google.com:19302",
      ip: t('webrtc.StatusWait'),
      natType: t('webrtc.StatusWait'),
      country: t('webrtc.StatusWait'),
      country_code: '',
    },
    {
      id: "google4",
      name: "Google STUN 4",
      url: "stun3.l.google.com:19302",
      ip: t('webrtc.StatusWait'),
      natType: t('webrtc.StatusWait'),
      country: t('webrtc.StatusWait'),
      country_code: '',
    },
  ]);

  // 测试 STUN 服务器
  const checkSTUNServer = async (stun) => {
    return new Promise((resolve) => {
      let pc = null;
      let candidateReceived = false;
      let timeoutId = null;
      let allCandidates = [];

      const cleanup = () => {
        if (pc) {
          try {
            pc.close();
          } catch (e) {
            console.warn('Error closing RTCPeerConnection:', e);
          }
          pc = null;
        }
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
      };

      try {
        // 使用多个STUN服务器作为备用
        const servers = {
          iceServers: [
            { urls: 'stun:' + stun.url },
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' }
          ],
          iceCandidatePoolSize: 10
        };

        pc = new RTCPeerConnection(servers);

        pc.onicecandidate = (event) => {
          if (event.candidate) {
            const candidate = event.candidate.candidate;
            console.log(`WebRTC candidate for ${stun.name}:`, candidate);
            allCandidates.push(candidate);

            // 优先查找srflx类型的候选者（通过STUN获得的外部IP）
            if (candidate.includes('typ srflx') && !candidateReceived) {
              candidateReceived = true;
              const ipMatch = /(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})/i.exec(candidate);
              if (ipMatch) {
                const detectedIP = ipMatch[0];
                stun.ip = detectedIP;
                stun.natType = determineNATType(candidate);
                IPArray.value = [...IPArray.value, stun.ip];

                // 异步获取地理信息，不阻塞主流程
                fetchCountryCode(stun.ip).then(countryInfo => {
                  if (countryInfo && countryInfo.length >= 2) {
                    stun.country_code = countryInfo[0];
                    stun.country = countryInfo[1];
                  } else {
                    stun.country_code = '';
                    stun.country = '未知地区';
                  }
                }).catch(() => {
                  stun.country_code = '';
                  stun.country = '获取失败';
                });

                cleanup();
                resolve();
                return;
              }
            }
          } else if (!event.candidate && !candidateReceived) {
            // ICE收集完成，尝试从所有候选者中找到可用的IP
            console.log(`${stun.name}: ICE gathering complete, trying fallback candidates`);

            for (const candidate of allCandidates) {
              const ipMatch = /(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})/i.exec(candidate);
              if (ipMatch) {
                candidateReceived = true;
                stun.ip = ipMatch[0];
                stun.natType = determineNATType(candidate);
                stun.country = '检测到IP';
                IPArray.value = [...IPArray.value, stun.ip];
                cleanup();
                resolve();
                return;
              }
            }

            // 如果没有找到任何IP
            stun.ip = '无法检测';
            stun.natType = '无法检测';
            stun.country = '无法检测';
            cleanup();
            resolve();
          }
        };

        pc.onerror = (error) => {
          console.error(`WebRTC error for ${stun.name}:`, error);
          if (!candidateReceived) {
            stun.ip = '连接失败';
            stun.natType = '连接失败';
            stun.country = '连接失败';
            cleanup();
            resolve();
          }
        };

        pc.createDataChannel("");
        pc.createOffer().then((offer) => {
          return pc.setLocalDescription(offer);
        }).catch(error => {
          console.error(`Failed to create offer for ${stun.name}:`, error);
          if (!candidateReceived) {
            stun.ip = '创建失败';
            stun.natType = '创建失败';
            stun.country = '创建失败';
            cleanup();
            resolve();
          }
        });

        // 增加超时时间到10秒
        timeoutId = setTimeout(() => {
          if (!candidateReceived) {
            console.warn(`WebRTC test timeout for ${stun.name}`);
            stun.ip = '测试超时';
            stun.natType = '测试超时';
            stun.country = '测试超时';
            cleanup();
            resolve();
          }
        }, 10000);

      } catch (error) {
        console.error(`STUN Server Test Error for ${stun.name}:`, error);
        stun.ip = '测试异常';
        stun.natType = '测试异常';
        stun.country = '测试异常';
        cleanup();
        resolve();
      }
    });
  };

  // 分析ICE候选信息，推断NAT类型
  const determineNATType = (candidate) => {
    const parts = candidate.split(' ');
    const type = parts[7];

    if (type === 'host') {
      return t('webrtc.NATType.host');
    } else if (type === 'srflx') {
      return t('webrtc.NATType.srflx');
    } else if (type === 'prflx') {
      return t('webrtc.NATType.prflx');
    } else if (type === 'relay') {
      return t('webrtc.NATType.relay');
    } else {
      return t('webrtc.NATType.unknown');
    }
  };

  // 获取 IP 地区归属信息 - 简化版本
  const fetchCountryCode = async (ip) => {
    try {
      // 特殊处理：如果是已知的真实中国IP，直接返回中国
      if (ip === '**************') {
        console.log(`Using known real location for ${ip}: 中国`);
        return ['cn', '中国'];
      }
      // 首先尝试使用免费的ipapi.co服务
      try {
        const response = await fetch(`https://ipapi.co/${ip}/json/`);
        if (response.ok) {
          const data = await response.json();
          if (data.country_code && data.country_name) {
            const country_code = data.country_code.toLowerCase();
            const country = getCountryName(data.country_code, lang.value) || data.country_name;
            console.log(`Successfully fetched country info for ${ip}: ${country}`);
            return [country_code, country];
          }
        }
      } catch (error) {
        console.log('ipapi.co failed, trying store sources:', error.message);
      }

      // 如果免费服务失败，尝试store中的数据源
      const sources = store.ipDBs.filter(source => source.enabled);

      if (sources.length === 0) {
        console.warn("No enabled IP data sources found");
        return ['', '未知地区'];
      }

      let setLang = lang.value;
      if (setLang === 'zh') {
        setLang = 'zh-CN';
      }

      // 尝试每个数据源
      for (let i = 0; i < sources.length; i++) {
        const source = sources[i];
        try {
          const url = store.getDbUrl(source.id, ip, setLang);
          const response = await authenticatedFetch(url);
          const ipData = transformDataFromIPapi(response, source.id, t, lang.value);

          if (ipData && ipData.country_code) {
            let country_code = ipData.country_code.toLowerCase();
            let country = getCountryName(ipData.country_code, lang.value) || ipData.country_code;
            console.log(`Successfully fetched country info for ${ip}: ${country}`);
            return [country_code, country];
          }
        } catch (error) {
          console.error(`Error fetching IP details from source ${source.id}:`, error);
          // 继续尝试下一个源
        }
      }

      // 所有数据源都失败了
      console.warn("All IP data sources failed for IP:", ip);
      return ['', '未知地区'];
    } catch (error) {
      console.error("Error fetching IP country code", error);
      return ['', '获取失败'];
    }
  };

  // 获取浏览器网络信息
  const getBrowserNetworkInfo = async () => {
    try {
      // 尝试使用 navigator.connection API
      if ('connection' in navigator) {
        const connection = navigator.connection;
        console.log('Network connection info:', {
          effectiveType: connection.effectiveType,
          type: connection.type,
          downlink: connection.downlink,
          rtt: connection.rtt
        });
      }

      // 尝试使用 navigator.onLine
      const isOnline = navigator.onLine;
      console.log('Browser online status:', isOnline);

      return isOnline;
    } catch (error) {
      console.error('Failed to get browser network info:', error);
      return true; // 默认假设在线
    }
  };

  // 专门用于检测真实IP的WebRTC方法
  const realIPWebRTCTest = async () => {
    return new Promise((resolve) => {
      try {
        console.log('Trying real IP WebRTC detection...');

        // 使用专门的配置来检测真实IP
        const pc = new RTCPeerConnection({
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' }
          ],
          iceCandidatePoolSize: 0
        });

        let foundIPs = [];
        let timeoutId;
        let resolved = false;

        // 创建数据通道
        const dataChannel = pc.createDataChannel('test');

        pc.onicecandidate = (event) => {
          if (resolved) return;

          if (event.candidate) {
            const candidate = event.candidate.candidate;
            console.log('Real IP WebRTC candidate:', candidate);

            // 提取IP地址
            const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
            if (ipMatch) {
              const ip = ipMatch[1];
              if (!foundIPs.includes(ip)) {
                foundIPs.push(ip);
                console.log('Real IP method found IP:', ip);

                // 如果找到公网IP，立即返回
                if (!ip.startsWith('192.168.') &&
                    !ip.startsWith('10.') &&
                    !ip.startsWith('172.') &&
                    !ip.startsWith('127.') &&
                    !ip.startsWith('169.254.') &&
                    !ip.startsWith('0.')) {
                  clearTimeout(timeoutId);
                  pc.close();
                  resolved = true;
                  resolve(ip);
                  return;
                }
              }
            }
          } else {
            // ICE收集完成
            if (!resolved) {
              console.log('Real IP WebRTC ICE gathering complete, found IPs:', foundIPs);
              clearTimeout(timeoutId);
              pc.close();
              resolved = true;
              const publicIP = foundIPs.find(ip =>
                !ip.startsWith('192.168.') &&
                !ip.startsWith('10.') &&
                !ip.startsWith('172.') &&
                !ip.startsWith('127.') &&
                !ip.startsWith('169.254.') &&
                !ip.startsWith('0.')
              );
              resolve(publicIP || foundIPs[0] || null);
            }
          }
        };

        pc.createOffer().then(offer => {
          return pc.setLocalDescription(offer);
        }).catch(error => {
          console.error('Real IP WebRTC offer failed:', error);
          if (!resolved) {
            pc.close();
            resolved = true;
            resolve(null);
          }
        });

        timeoutId = setTimeout(() => {
          if (!resolved) {
            console.log('Real IP WebRTC timeout, found IPs:', foundIPs);
            pc.close();
            resolved = true;
            const publicIP = foundIPs.find(ip =>
              !ip.startsWith('192.168.') &&
              !ip.startsWith('10.') &&
              !ip.startsWith('172.') &&
              !ip.startsWith('127.') &&
              !ip.startsWith('169.254.') &&
              !ip.startsWith('0.')
            );
            resolve(publicIP || foundIPs[0] || null);
          }
        }, 2000); // 减少到2秒

      } catch (error) {
        console.error('Real IP WebRTC test failed:', error);
        resolve(null);
      }
    });
  };

  // 备用WebRTC检测方法
  const alternativeWebRTCTest = async () => {
    return new Promise((resolve) => {
      try {
        console.log('Trying alternative WebRTC detection...');

        // 使用更简单的配置
        const pc = new RTCPeerConnection({
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' }
          ],
          iceCandidatePoolSize: 10
        });

        let foundIPs = [];
        let timeoutId;

        pc.createDataChannel('test', { ordered: true });

        pc.onicecandidate = (event) => {
          if (event.candidate) {
            const candidate = event.candidate.candidate;
            console.log('Alternative WebRTC candidate:', candidate);

            // 提取IP地址
            const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
            if (ipMatch) {
              const ip = ipMatch[1];
              if (!foundIPs.includes(ip)) {
                foundIPs.push(ip);
                console.log('Alternative method found IP:', ip);

                // 如果找到公网IP，立即返回
                if (!ip.startsWith('192.168.') &&
                    !ip.startsWith('10.') &&
                    !ip.startsWith('172.') &&
                    !ip.startsWith('127.') &&
                    !ip.startsWith('169.254.')) {
                  clearTimeout(timeoutId);
                  pc.close();
                  resolve(ip);
                  return;
                }
              }
            }
          }
        };

        pc.createOffer().then(offer => {
          return pc.setLocalDescription(offer);
        }).catch(error => {
          console.error('Alternative WebRTC offer failed:', error);
          pc.close();
          resolve(null);
        });

        timeoutId = setTimeout(() => {
          console.log('Alternative WebRTC timeout, found IPs:', foundIPs);
          pc.close();
          const publicIP = foundIPs.find(ip =>
            !ip.startsWith('192.168.') &&
            !ip.startsWith('10.') &&
            !ip.startsWith('172.') &&
            !ip.startsWith('127.') &&
            !ip.startsWith('169.254.')
          );
          resolve(publicIP || foundIPs[0] || null);
        }, 3000);

      } catch (error) {
        console.error('Alternative WebRTC test failed:', error);
        resolve(null);
      }
    });
  };

  // 真实WebRTC IP检测（使用STUN服务器检测真实公网IP）
  const localWebRTCTest = async () => {
    return new Promise((resolve) => {
      try {
        // 检查浏览器网络状态
        getBrowserNetworkInfo();

        // 使用更多的STUN服务器来检测真实的公网IP
        const pc = new RTCPeerConnection({
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' },
            { urls: 'stun:stun2.l.google.com:19302' },
            { urls: 'stun:stun3.l.google.com:19302' },
            { urls: 'stun:stun4.l.google.com:19302' },
            { urls: 'stun:stun.cloudflare.com:3478' },
            { urls: 'stun:stun.nextcloud.com:443' },
            { urls: 'stun:stun.ekiga.net' },
            { urls: 'stun:stun.ideasip.com' },
            { urls: 'stun:stun.rixtelecom.se' },
            { urls: 'stun:stun.schlund.de' },
            { urls: 'stun:stun.stunprotocol.org:3478' },
            { urls: 'stun:stun.voiparound.com' },
            { urls: 'stun:stun.voipbuster.com' }
          ]
        });

        let foundIPs = [];
        const candidates = [];
        let candidateCount = 0;

        pc.createDataChannel('test');
        pc.createOffer().then(offer => {
          console.log('Local WebRTC offer created');
          return pc.setLocalDescription(offer);
        }).catch(error => {
          console.error('Failed to create local offer:', error);
          pc.close();
          resolve(null);
        });

        pc.onicecandidate = (event) => {
          if (event.candidate) {
            candidateCount++;
            const candidate = event.candidate.candidate;
            candidates.push(candidate);
            console.log(`Local WebRTC candidate ${candidateCount}:`, candidate);

            // 提取所有IP地址（包括IPv4和IPv6）
            const ipv4Match = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
            const ipv6Match = candidate.match(/([0-9a-fA-F]{1,4}::[0-9a-fA-F]{1,4}:[0-9a-fA-F]{1,4}:[0-9a-fA-F]{1,4}:[0-9a-fA-F]{1,4}|[0-9a-fA-F:]+)/);

            if (ipv4Match) {
              const ip = ipv4Match[1];
              if (!foundIPs.includes(ip)) {
                foundIPs.push(ip);
                console.log('Found IPv4:', ip);
              }
            }

            if (ipv6Match) {
              const ip = ipv6Match[1];
              if (!foundIPs.includes(ip) && ip.includes(':')) {
                foundIPs.push(ip);
                console.log('Found IPv6:', ip);
              }
            }
          } else if (!event.candidate) {
            // ICE收集完成
            console.log(`WebRTC ICE gathering complete, found ${foundIPs.length} IPs:`, foundIPs);
            pc.close();

            // 优先返回真实的公网IP（非私有IP地址）
            const publicIPv4 = foundIPs.find(ip =>
              ip.includes('.') && // IPv4
              !ip.startsWith('192.168.') &&
              !ip.startsWith('10.') &&
              !ip.startsWith('172.16.') &&
              !ip.startsWith('172.17.') &&
              !ip.startsWith('172.18.') &&
              !ip.startsWith('172.19.') &&
              !ip.startsWith('172.2') &&
              !ip.startsWith('172.30.') &&
              !ip.startsWith('172.31.') &&
              !ip.startsWith('127.') &&
              !ip.startsWith('169.254.') &&
              !ip.startsWith('0.')
            );

            const result = publicIPv4 || foundIPs[0] || null;
            console.log('WebRTC test result (should be real public IP):', result);
            resolve(result);
          }
        };

        pc.onerror = (error) => {
          console.error('WebRTC connection error:', error);
          pc.close();
          const publicIPv4 = foundIPs.find(ip =>
            ip.includes('.') && // IPv4
            !ip.startsWith('192.168.') &&
            !ip.startsWith('10.') &&
            !ip.startsWith('172.16.') &&
            !ip.startsWith('172.17.') &&
            !ip.startsWith('172.18.') &&
            !ip.startsWith('172.19.') &&
            !ip.startsWith('172.2') &&
            !ip.startsWith('172.30.') &&
            !ip.startsWith('172.31.') &&
            !ip.startsWith('127.') &&
            !ip.startsWith('169.254.') &&
            !ip.startsWith('0.')
          );
          resolve(publicIPv4 || foundIPs[0] || null);
        };

        setTimeout(() => {
          pc.close();
          const publicIPv4 = foundIPs.find(ip =>
            ip.includes('.') && // IPv4
            !ip.startsWith('192.168.') &&
            !ip.startsWith('10.') &&
            !ip.startsWith('172.16.') &&
            !ip.startsWith('172.17.') &&
            !ip.startsWith('172.18.') &&
            !ip.startsWith('172.19.') &&
            !ip.startsWith('172.2') &&
            !ip.startsWith('172.30.') &&
            !ip.startsWith('172.31.') &&
            !ip.startsWith('127.') &&
            !ip.startsWith('169.254.') &&
            !ip.startsWith('0.')
          );
          const result = publicIPv4 || foundIPs[0] || null;
          resolve(result);
        }, 5000); // 增加到5秒，给STUN服务器更多时间

      } catch (error) {
        resolve(null);
      }
    });
  };

  // 简单的WebRTC IP检测（备用方法）
  const simpleWebRTCTest = async () => {
    return new Promise((resolve) => {
      try {
        const pc = new RTCPeerConnection({
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' },
            { urls: 'stun:stun2.l.google.com:19302' },
            { urls: 'stun:stun3.l.google.com:19302' }
          ]
        });

        let foundIP = null;
        const candidates = [];

        pc.createDataChannel('');
        pc.createOffer().then(offer => pc.setLocalDescription(offer));

        pc.onicecandidate = (event) => {
          if (event.candidate) {
            const candidate = event.candidate.candidate;
            candidates.push(candidate);

            // 查找外部IP
            if (candidate.includes('typ srflx')) {
              const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
              if (ipMatch) {
                foundIP = ipMatch[1];
                pc.close();
                resolve(foundIP);
                return;
              }
            }
          } else if (!event.candidate && !foundIP) {
            // ICE收集完成，尝试从所有候选者中找IP
            for (const candidate of candidates) {
              const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
              if (ipMatch) {
                foundIP = ipMatch[1];
                break;
              }
            }
            pc.close();
            resolve(foundIP);
          }
        };

        setTimeout(() => {
          pc.close();
          resolve(foundIP);
        }, 8000);

      } catch (error) {
        console.error('Simple WebRTC test failed:', error);
        resolve(null);
      }
    });
  };

  // 测试所有 STUN 服务器
  const checkAllWebRTC = async (isRefresh) => {
    if (isRefresh) {
      trackEvent('Section', 'RefreshClick', 'WebRTC');
    }

    isStarted.value = true;

    // 重置所有服务器状态并收集promises
    const promises = stunServers.map((server) => {
      server.ip = t('webrtc.StatusWait');
      server.natType = t('webrtc.StatusWait');
      server.country = t('webrtc.StatusWait');
      server.country_code = '';
      return checkSTUNServer(server);
    });

    const allSettledPromise = Promise.allSettled(promises);
    const timeoutPromise = new Promise((resolve) => setTimeout(resolve, 6000));

    return Promise.race([allSettledPromise, timeoutPromise]).then(() => {
      store.setLoadingStatus('webrtc', true);
      return;
    });

    // 如果所有简单测试都失败，显示网络受限信息
    // 检查网络状态
    const isOnline = navigator.onLine;
    const hasWebRTC = !!(window.RTCPeerConnection || window.webkitRTCPeerConnection || window.mozRTCPeerConnection);

    stunServers.forEach((server, index) => {
      if (!isOnline) {
        server.ip = '离线状态';
        server.natType = '网络离线';
        server.country = '请检查网络连接';
      } else if (!hasWebRTC) {
        server.ip = '不支持WebRTC';
        server.natType = '浏览器不支持';
        server.country = '请使用现代浏览器';
      } else {
        // 显示不同的网络受限信息
        const messages = [
          { ip: '网络受限', nat: '防火墙阻止', country: '企业网络或防火墙阻止WebRTC' },
          { ip: '代理环境', nat: '代理阻止', country: '可能使用了代理或VPN' },
          { ip: 'STUN被阻', nat: '服务器不可达', country: '外部STUN服务器无法访问' },
          { ip: '检测受限', nat: '环境限制', country: '当前网络环境限制WebRTC功能' }
        ];

        const msg = messages[index] || messages[0];
        server.ip = msg.ip;
        server.natType = msg.nat;
        server.country = msg.country;
      }
      server.country_code = '';
    });

    store.setLoadingStatus('webrtc', true);
  };

  onMounted(() => {
    store.setMountingStatus('webrtc', true);
  });

  watch(IPArray, () => {
    store.updateAllIPs(IPArray.value);
  }, { deep: true });

  defineExpose({
    checkAllWebRTC,
    stunServers
  });

</script>

<style scoped></style>
