{"user": {"SignIn": "登录/注册", "SignInWithGoogle": "使用 Google 登录", "SignInWithGithub": "使用 GitHub 登录", "SignOut": "登出", "SignInToView": "登录后查看", "SignInToUse": "请登录后使用", "InvalidUserToken": "无效的用户 Token", "MyAchievements": "我的成就", "Fields": {"User": "用户", "CreatedAt": "创建于", "LastLogin": "最后登录", "Level": "用户等级", "FunctionUses": "高级功能使用", "Times": "次", "Fetching": "获取中..."}, "Level": {"Standard": "标准用户", "Premium": "高级用户", "Owner": "管理员", "Developer": "开发者", "HonoraryMember": "荣誉用户"}, "Benefits": {"Title": "用户权益", "Benefit": "权益", "Note1": "这是一个免费的开源产品。", "Note2": "登录后可以解锁更多功能：", "Benefit1": "完整显示IP数据字段", "Benefit2": "使用隐身测试功能", "Benefit3": "查看成就勋章", "Benefit4": "减少功能限制", "FootNote": "更多权益开发中..."}, "Achievements": {"Title": "成就", "Note": "使用过程中可以获得成就，这里显示你的所有成就。", "Get": "已获得", "GetCount": "已获得的成就数量", "NotGet": "未获得", "NotGetCount": "未获得的成就数量", "FooterNote": "可能有限时成就。", "Congrats": "🎉 恭喜", "CongratsMessage": "你获得了一个新成就！", "NewAchievementIs": "新成就是：", "Type": {"IAmHuman": {"Title": "有头有脸", "Meet": "登录后使用 IPCheck.ing"}, "BarelyEnough": {"Title": "凑凑合合", "Meet": "下载速度超过 100Mb/s"}, "RapidPace": {"Title": "快马加鞭", "Meet": "下载速度超过 500Mb/s"}, "TorrentFlow": {"Title": "一泻千里", "Meet": "下载速度超过 1000Mb/s"}, "SteadyGoing": {"Title": "普普通通", "Meet": "上传速度超过 50Mb/s"}, "TooFastTooSimple": {"Title": "港记难追", "Meet": "上传速度超过 200Mb/s"}, "SwiftAscent": {"Title": "日行千里", "Meet": "上传速度超过 1000Mb/s"}, "SurfaceCheck": {"Title": "蜻蜓点水", "Meet": "完成一项安全检查清单"}, "HalfwayThere": {"Title": "半半拉拉", "Meet": "完成 50% 安全检查清单"}, "FullySecured": {"Title": "滴水不漏", "Meet": "完成全部安全检查清单"}, "JustInCase": {"Title": "以防万一", "Meet": "完成一次隐身测试"}, "HiddenWell": {"Title": "不露声色", "Meet": "隐身测试中代理和 VPN 概率都是 0"}, "SlipUp": {"Title": "露出马脚", "Meet": "隐身测试中代理或 VPN 概率大于 50%"}, "CleverTrickery": {"Title": "奇技淫巧", "Meet": "查看了所有快捷键"}, "EnergySaver": {"Title": "省油的灯", "Meet": "设置程序不自动运行"}, "ResourceHog": {"Title": "费油的车", "Meet": "设置多次刷新可用性"}, "MakingBigNews": {"Title": "搞大新闻", "Meet": "高级功能调用超过 1000 次"}, "GenerousDonor": {"Title": "慷慨解囊", "Meet": "给 IPCheck.ing 项目捐赠过"}, "ItIsOpen": {"Title": "是开放的", "Meet": "在封锁测试中，查询到了一个被封锁的网站"}, "CuriousCat": {"Title": "好奇的猫", "Meet": "在 Whois 查询中，查询了 IPCheck.ing 的信息"}, "CrossingTheWall": {"Title": "八仙过墙", "Meet": "在规则测试中，8 个测试的 IP 全部不相同"}}}}, "nav": {"id": "nav", "Title": "IPCheck.ing", "Navigation": "导航", "IPInfo": "IP 信息", "Connectivity": "网络连通性", "WebRTC": "WebRTC 测试", "DNSLeakTest": "DNS 泄漏测试", "SpeedTest": "网速测试", "PingTest": "全球延迟", "MTRTest": "MTR 测试", "RuleTest": "分流测试", "AdvancedTools": "高级工具", "preferences": {"title": "偏好设置", "preferenceTips": "设置保存在浏览器中，部分选项需刷新页面生效。", "colorScheme": "颜色方案", "systemAuto": "跟随系统", "colorLight": "白天", "colorDark": "黑夜", "ipSourcesToCheck": "IP 检测服务器数量", "ipSourcesToCheckTips": "选择IP检测服务器数量。", "appSettings": "程序设置", "autoRun": "自动运行", "autoRunTips": "关闭后只进行IP检测，不自动运行其他测试。", "showMap": "显示地图", "showMapTips": "在IP信息卡片显示地图。", "simpleMode": "简洁模式", "simpleModeTips": "减少IP卡片内容，仅手机生效。", "connectivityAutoRefresh": "多次刷新可用性检测", "connectivityAutoRefreshTips": "启动时运行5次检测，显示最小延迟值。", "popupConnectivityNotifications": "显示可用性检测气泡", "popupConnectivityNotificationsTips": "首次检测时显示气泡提示。", "ipDB": "IP 解析数据源", "ipDBTips": "选择默认IP地理位置数据源，不可用时自动使用备选源。", "language": "语言设置", "languageTips": "需刷新浏览器生效。"}}, "securitychecklist": {"Title": "安全检查清单", "Note": "📃 全面的网络安全检查清单，帮助你保护隐私和提高上网安全。", "Note2": "内容较多，可以分次检查，进度会自动保存。", "Progress": "检查进度", "Item": "项目", "Items": "个项目", "Priority": "级别", "Ignore": "忽略", "alert-total": "安全检查一共有", "alert-checked": "你已经完成了", "alert-ignored": "被忽略", "alert-unchecked": "个未进行检查。", "Checked": "已检查", "Ignored": "已忽略", "Unchecked": "未检查", "Basic": "基础", "Optional": "可选", "Essential": "必要", "Advanced": "高级", "Reset": "重置", "ShowAll": "显示所有", "ShowUnchecked": "显示未检查", "ShowIgnored": "显示已忽略", "ShowChecked": "显示已检查", "Loading": "加载中..."}, "curl": {"Title": "命令行 API", "Note1": "哟，原来你也喜欢用终端？", "Note2_1": "你可以在终端里，通过", "Note2_2": "命令获取本机的 IP 地址", "Note3": "是可选路径，添加后可获取到 IP 的地理信息", "getIPv4": "获取本机 IPv4 地址", "getIPv6": "获取本机 IPv6 地址", "get6and4": "获取本机优先网络出口的 IP 地址", "notAvailable": "此功能暂未开放，敬请期待"}, "browserinfo": {"Title": "浏览器信息", "Note": "💻 显示你的浏览器基本信息和指纹。指纹越独特，越容易被网站跟踪。", "Note2": "这里使用简单方法计算指纹，仅供参考。", "browser": {"Infos": "浏览器信息", "browserName": "浏览器名称", "browserVersion": "版本", "engineName": "引擎", "engineVersion": "版本", "osName": "系统", "osVersion": "版本", "deviceModel": "型号", "deviceVendor": "制造商", "cpuArchitecture": "CPU 架构", "gpu": "GPU", "cpuCores": "CPU 核心数", "language": "语言代码", "cookieEnabled": "是否启用 <PERSON>ie", "cookieEnabledTrue": "是", "cookieEnabledFalse": "否", "memory": "设备内存", "platform": "平台信息", "timezone": "时区", "screenResolution": "屏幕分辨率", "colorDepth": "颜色深度", "pixelRatio": "像素比", "touchSupport": "触摸支持", "onlineStatus": "在线状态", "javaEnabled": "Java 支持", "doNotTrack": "请勿跟踪", "webGL": "WebGL 支持", "canvas": "Canvas 指纹", "webRTC": "WebRTC 支持", "localStorage": "本地存储", "sessionStorage": "会话存储", "indexedDB": "IndexedDB", "webWorkers": "Web Workers", "serviceWorkers": "Service Workers", "webAssembly": "WebAssembly", "permissions": "权限 API", "battery": "电池状态", "connection": "网络连接", "plugins": "浏览器插件", "mimeTypes": "MIME 类型"}, "fingerprint": {"Infos": "指纹信息", "fingerprint": "指纹编码", "changeOption": "可以变更计算数据，观察指纹变化（建议用隐私模式对比）。", "browserTips": "某些浏览器（如Safari）为防跟踪，每次计算可能得到不同指纹值。"}, "options": {"audio": "音频", "canvas": "画布", "fonts": "字体", "hardware": "硬件", "locales": "语言", "permissions": "权限", "plugins": "插件", "screen": "屏幕", "system": "浏览器版本", "webgl": "WebGL", "math": "运算"}, "calError": "浏览器信息计算失败，请刷新重试。", "calculating": "计算中..."}, "invisibilitytest": {"Title": "隐身测试", "Note": "检测你使用代理或VPN时是否真的隐身，找出可能的泄漏风险。", "Note2": "点击运行后，约10秒内完成测试，会收集你的IP和浏览器信息进行分析。", "Run": "运行测试", "fetchError": "无法获取测试结果", "yourIP": "你的主测 IP 是", "proxyScore": "代理概率", "VPNScore": "VPN 概率", "isProxy": "检测到代理使用：你正在使用代理服务器。", "notProxy": "未发现你使用了代理。", "isVPN": "🧿检测到使用VPN：你正在使用VPN进行连接。", "notVPN": "未发现你使用了 VPN。", "isBoth": "检测到代理/VPN使用：你正在使用代理或VPN服务。", "notDetected": "未检测到代理或VPN使用。", "itemName": "测试项目", "itemProxyResult": "测试结果", "itemComment": "分析", "agreement": "同意信息收集", "blocklist": {"title": "VPN/代理名单", "proxy": "你的 IP 在 VPN/代理名单中。", "notProxy": "你的 IP 不在 VPN/代理名单中"}, "headers": {"title": "HTTP 头信息", "proxy": "你的 HTTP 头信息中包含了代理信息，你可能正在使用代理。", "notProxy": "你的 HTTP 头信息中没有代理信息。"}, "datacenter": {"title": "数据中心 IP", "proxy": "你的 IP 是数据中心 IP，普通用户的 IP 通常不会是数据中心。检测到的数据中心是：", "notProxy": "你的 IP 不是数据中心 IP"}, "tcp": {"title": "TCP 指纹", "proxy": "你发起连接的系统与你的电脑系统不同，你可能正在使用代理。尽管系统判断不一定准确，但可以肯定两个系统并不相同。", "computer": "你的电脑系统是：", "server": "你发起连接的系统可能是：", "notProxy": "你发起连接的系统与你的电脑系统相同。"}, "timezone": {"title": "时区差异", "proxy": "你的时区与你的 IP 地理位置不匹配，你可能正在使用代理。", "computer": "你的时区是：", "server": "你的 IP 地理位置时区是：", "notProxy": "你的时区与你的 IP 地理位置匹配。"}, "net": {"title": "网络解析", "proxy": "从网络解析上发现你可能使用了代理或 VPN", "notProxy": "从网络解析上未发现你使用代理或 VPN 的证据。"}, "webrtc": {"title": "WebRTC 检测", "proxy": "WebRTC 发现你存在多个不同地区的 IP，你可能使用了代理或 VPN。", "ipsAre": "IP 分别是：", "notProxy": "WebRTC 检测未发现存在多个不同地区的 IP。"}, "flow": {"title": "流量分析", "proxy": "流量分析发现你可能使用了代理或 VPN。", "notProxy": "流量分析未发现你使用代理或 VPN 的证据。"}, "latency": {"title": "延迟分析", "proxy": "延迟分析发现你可能使用了代理或 VPN。", "fromTCP": "TCP 延迟值：", "fromWS": "WebSocket 延迟值：", "notProxy": "延迟分析未发现你使用代理或VPN的证据。"}, "highlatency": {"title": "高延迟分析", "proxy": "高延迟分析发现你可能使用了代理或VPN。", "notProxy": "高延迟分析未发现你使用代理或VPN的证据。"}}, "censorshipcheck": {"Title": "封锁测试", "Note": "🌍 检查网站在中国大陆、沙特阿拉伯、俄罗斯、土耳其等地区是否被封锁。测试组是审查严格的地区，控制组是相对自由的地区。", "Note2": "请输入 URL 或域名，开始封锁测试：", "Note3": "只有当同一个国家的所有服务器都无法访问时，才会判断为被封锁", "Run": "运行测试", "Placeholder": "URL 或域名", "Country": "地区", "Status": "状态", "City": "城市", "Network": "网络", "invalidURL": "无效的 URL 或域名", "fetchError": "无法获取测试结果", "isBlocked": "这个网站看起来在部分国家被封锁了，请参考上方的测试结果做最终判断。", "notBlocked": "这个网站看起来没有被封锁了，请参考上方的测试结果做最终判断。", "isDown": "无法判断这个网站是否被封锁，因为看起来它在全球许多位置都无法访问。如果你输入的地址是正确的，有可能网站本身出现了问题导致无法访问。", "Timeout": "测试超时", "TestGroup": "测试组", "ControlGroup": "控制组"}, "advancedtools": {"Title": "高级工具", "Note": "⚙ 专业网络测试工具集合。", "PingTestNote": "全球延迟测试", "MTRTestNote": "全球路由跟踪", "DNSResolverNote": "多渠道DNS解析", "RuleTestNote": "代理分流规则测试", "CensorshipCheck": "网站封锁检测", "Whois": "域名/IP信息查询", "InvisibilityTest": "代理隐身检测", "MacChecker": "MAC地址查询", "BrowserInfo": "浏览器信息检测", "SecurityChecklist": "安全检查清单"}, "macchecker": {"Title": "MAC 地址查询", "Note": "🗃 查询MAC地址的制造商和相关信息，支持完整地址或前缀查询。MAC地址是网络设备的物理地址，单播/多播表示传输方式。", "Note2": "请输入一个物理地址，开始查询：", "Run": "查询", "Placeholder": "F0:2F:4B:01:0A:AA", "invalidMAC": "无效的物理地址", "fetchError": "无法获取查询结果", "company": "制造商", "macPrefix": "物理地址前缀", "address": "注册地址", "country": "注册国家/地区", "blockStart": "起始区块", "blockEnd": "结束区块", "blockSize": "区块大小", "blockType": "区块类型", "blockRange": "区块范围", "isRand": "随机地址", "isPrivate": "私有数据", "isMulticast": "多播地址", "isUnicast": "单播地址", "isGlobal": "全球地址", "isLocal": "本地地址", "property": "地址属性", "value": "值", "manufacturer": "制造商信息"}, "whois": {"Title": "Whois 查询", "Note": "🏛 查询域名或IP的注册信息，包括注册商、注册日期、过期日期等。注册商是域名服务提供商，过期日期是域名到期时间。", "Note2": "请输入域名或 IP，开始进行查询：", "Note3": "Whois 信息包含多种类型的字段，以下为原始文本信息", "Placeholder": "域名或 IP", "Run": "查询", "invalidURL": "无效的域名或 IP", "fetchError": "无法获取查询结果或查询结果为空", "Provider": "信息服务商"}, "dnsresolver": {"Title": "DNS 解析", "Note": "🌎 通过全球多个DNS服务商检查域名解析结果，帮助发现DNS污染问题。DNS服务商是域名解析服务提供商，记录类型包括A、AAAA、CNAME等。", "Note2": "请输入 URL 或域名，开始进行解析：", "Placeholder": "URL 或域名", "Run": "解析", "invalidURL": "无效的 URL 或域名", "fetchError": "无法获取解析结果", "Provider": "DNS 服务商", "Result": "解析结果", "Record": "记录"}, "ruletest": {"Title": "分流测试", "Name": "检测", "Note": "✈ 检测代理软件的域名分流规则是否正确配置。代理地区显示该域名通过哪个地区的代理访问。", "StatusWait": "待检测", "StatusError": "检测出错", "Country": "代理地区", "RefreshAll": "刷新所有"}, "Tooltips": {"RefreshIPCard": "刷新 IP 信息", "RefreshConnectivityTests": "刷新连通性测试", "RefreshWebRTC": "刷新 WebRTC 测试", "RefreshDNSLeakTest": "刷新 DNS 泄漏测试", "SpeedTestButton": "开始/暂停网速测试", "ToggleMaps": "打开/关闭地图显示", "SourceSelect": "选择 IP 归属地数据来源", "ShowASNInfo": "显示 AS 详细信息", "CopyIP": "复制 IP 地址", "InfoMask": "隐藏信息", "QueryIP": "查询 IP 信息", "RefreshRuleTests": "刷新规则测试", "qualityScoreExplain": "IP质量分综合多个因素计算，分数越高质量越好，被风控的概率越低。", "qualityScoreExplainDetail": "IP质量分说明：\n• 分数越高，IP质量越好\n• 高分IP不容易被网站拦截\n• 低分IP容易被当作机器人\n\n评分等级：\n• 90分以上：优秀 - IP质量很好，不容易被网站拦截\n• 80-89分：良好 - IP质量较好，很少被拦截\n• 70-79分：一般 - IP质量中等，偶尔可能被拦截\n• 60-69分：较差 - IP质量不好，容易被当作机器人\n• 60分以下：很差 - IP质量很差，经常被网站拦截", "GithubLink": "查看源代码"}, "ipInfos": {"id": "ipinfos", "database": "数据源", "Title": "IP 信息", "Notes": "🗺 检测你的IP地址并显示地理位置信息，支持IPv4和IPv6。ASN是自治系统编号，ISP是网络服务商，质量分反映IP被风控的风险。📊IP质量评分系统详解", "loading": "正在加载...", "Simple": "简约", "Map": "地图", "MapUnavailable": "地图不可用", "Source": "IP 来源", "IP": "IP", "Country": "地区", "Region": "省份", "City": "城市", "ISP": "网络", "ASN": "ASN", "IPv4Error": "获取失败或不存在 IPv4 地址", "IPv6Error": "获取失败或不存在 IPv6 地址", "SelectSource": "选择 IP 归属地数据来源", "type": "类型", "isProxy": "代理", "qualityScore": "IP 质量分", "qualityScoreUnknown": "未知分数", "ASNInfo": {"note": "AS相关数据：", "asnName": "AS 名称：", "asnOrgName": "AS 组织名称：", "estimatedUsers": "估计用户数：", "IPv4_Pct": "IPv4 流量占比：", "IPv6_Pct": "IPv6 流量占比：", "HTTP_Pct": "HTTP 流量占比：", "HTTPS_Pct": "HTTPS 流量占比：", "Desktop_Pct": "桌面设备占比：", "Mobile_Pct": "移动设备占比：", "Bot_Pct": "机器人流量占比：", "Human_Pct": "人类流量占比："}, "proxyDetect": {"yes": "是代理或 VPN", "maybe": "可能是代理或 VPN", "no": "不是代理或 VPN", "unknownProxyType": "未知类型", "unknownProtocol": "未知协议", "type": {"Residential": "住宅线路", "Wireless": "无线网络", "Business": "商业线路", "Hosting": "数据中心", "unknownType": "未知类型"}}}, "connectivity": {"id": "connectivity", "Title": "网络连通性", "Note": "📶 测试各大网站的连通性和延迟。延迟数值仅供参考，超时表示无法访问或响应过慢。", "StatusWait": "待检测", "StatusAvailable": "可用", "StatusUnavailable": "不可用", "StatusTimeout": "超时或不可用", "checking": "检查中...", "minTestTime": "最小测试时间：", "RefreshThisTest": "刷新此测试"}, "webrtc": {"id": "webrtc", "Title": "WebRTC 测试", "Note": "🎫 检测使用VPN/代理时是否存在真实IP泄漏风险。NAT类型显示网络连接方式，地区显示检测到的IP归属地，N/A表示无法确定。", "StatusWait": "待检测或连接错误", "StatusError": "测试出错", "NATType": {"srflx": "端口限制型或对称型", "prflx": "端口限制型", "relay": "对称型", "host": "全锥型", "unknown": "未知类型"}}, "dnsleaktest": {"id": "dnsleaktest", "Name": "检测", "Title": "DNS 泄漏测试", "Note": "⛔ 检测使用VPN/代理时是否存在DNS泄漏风险。通过访问测试IP，检查DNS解析是否通过本地运营商。", "Endpoint": "DNS 出口", "EndpointCountry": "出口地区", "StatusWait": "待检测", "StatusError": "测试出错"}, "speedtest": {"id": "speedtest", "Title": "网速测试", "Note": "⚡ 使用Cloudflare边缘网络测试网速，已优化测试速度提升用户体验。可选择不同包大小，注意流量消耗。延迟是响应时间，抖动是延迟的变化幅度。", "QuickTestMode": "快速测试模式 (更快体验)", "QuickTestHint": "下载3MB，上传1MB，延迟5次", "Download": "下载", "Upload": "上传", "Latency": "延迟", "Jitter": "抖动", "Unit": "Mbps", "StatusWait": "--", "StatusError": "测试出错", "videoStreaming": "在线视频：", "gaming": " ，在线游戏：", "rtc": " ，视频会议：", "score": "网速质量分数：", "resultNote": "。", "connectionFrom": "测试完成，本机IP：", "connectionTo": "，服务器：", "connectionEnd": "。", "quality": {"Good": "优秀", "Medium": "中等", "Bad": "较差"}}, "pingtest": {"id": "pingtest", "Title": "全球延迟测试", "Note": "📶 测试你的IP到全球各地服务器的网络延迟。", "Note2": "从世界各地服务器向你的IP发送数据包，测量往返时间。ms是毫秒，丢包率反映网络稳定性。", "Note3": "请选取你的 IP 地址进行测试：", "SelectIP": "选择一个 IP 地址", "Region": "地区", "MinDelay": "最小延迟（ms）", "MaxDelay": "最大延迟（ms）", "AvgDelay": "平均延迟（ms）", "TotalPackets": "总包数", "PacketLoss": "丢包率", "ReceivedPackets": "收到", "DroppedPackets": "丢失", "Run": "运行测试", "Error": "测试失败，看起来你的 IP 不允许进行 Ping 测试。"}, "mtrtest": {"id": "mtrtest", "Title": "MTR 测试", "Note": "🌏 从全球服务器对你的IP进行路由跟踪，诊断网络质量。", "Note2": "显示完整的网络路径，包括每一跳的延迟和丢包信息。每一跳代表数据经过的网络节点。", "Note3": "请选取你的 IP 地址进行测试：", "SelectIP": "选择一个 IP 地址", "Region": "地区", "Run": "运行测试", "Error": "测试失败，看起来你的 IP 不允许进行 MTR 测试。"}, "ipcheck": {"id": "ipcheck", "Title": "IP 查询", "Placeholder": "请输入 IP 地址", "Button": "查询", "Error": "请输入有效的 IPv4 或 IPv6 地址。"}, "alert": {"id": "alert", "refreshEverythingMessage": "正在刷新所有数据，请等待...", "refreshEverythingTitle": "正在刷新", "OhNo": "糟糕！", "Congrats": "恭喜呀！", "OhNo_Message": "连通性检测没有通过，部分网站无法访问。", "Congrats_Message": "所有的连通性检测均通过，你可以访问所有网站。", "maskedInfoTitle_1": "IP 隐藏成功", "maskedInfoMessage_1": "IP 信息已隐藏，截图时请注意隐私。", "maskedInfoTitle": "全部隐藏成功", "maskedInfoMessage": "所有信息已隐藏，现在可以安心截图了。", "unmaskedInfoTitle": "取消隐藏", "unmaskedInfoMessage": "信息已显示，截图时请注意隐私。", "SignInFailed": "登录失败", "SignInFailedReason": "原因"}, "shortcutKeys": {"id": "shortcutKeys", "GoToTop": "回到顶部", "GoToBottom": "回到底部", "ToggleDarkMode": "切换深色模式", "RefreshEverything": "刷新所有数据", "RefreshIPCard": "刷新某张 IP 卡片", "RefreshConnectivityTests": "刷新连通性测试", "RefreshWebRTC": "刷新 WebRTC 测试", "RefreshDNSLeakTest": "刷新 DNS 泄漏测试", "SpeedTestButton": "开始/暂停网速测试", "PingTest": "打开全球延迟测试面板", "MTRTest": "打开全球路由测试面板", "RuleTest": "打开规则测试面板", "DNSResolver": "打开 DNS 解析器面板", "ToggleMaps": "切换地图显示", "IPCheck": "IP 查询", "ToggleInfoMask": "切换信息隐藏", "Help": "显示快捷键", "HelpNote": "快捷键让操作更高效！", "GoNext": "下一张卡片", "GoPrevious": "上一张卡片", "RefreshRuleTests": "刷新规则测试", "CensorshipCheck": "封锁测试", "Preferences": "偏好设置", "About": "关于页面", "Whois": "Whois查询", "InvisibilityTest": "隐身测试", "MacChecker": "MAC地址查询", "BrowserInfo": "浏览器信息", "fullScreenAdvancedTools": "全屏高级工具", "Curl": "命令行API", "SecurityChecklist": "安全检查清单"}, "page": {"title": "𝓌𝑜𝒷MyIP - 查看我的 IP 地址 - 查询本机 IP 地址及归属地 - 查看 WebRTC 连接 IP - DNS 泄露检测 - 网速测试", "description": "开源的IP工具箱。查看IP地址、检测DNS泄漏、测试网速、查询域名信息等。", "keywords": "我的IP,IP工具,我的IP,IP查询,DNS泄漏测试,WebRTC测试,网速测试,DNS查询,Whois查询, <PERSON>测试, 隐私测试", "footerLink": "https://wobshare.us.kg", "copyRightName": "Originally from ", "copyRightLink": "https://ipcheck.ing", "copyRightLinkName": "IPCheck.ing"}, "helpModal": {"Title": "快捷键"}, "about": {"Title": "关于", "product1": "IPCheck.ing 是一个免费的 IP 工具箱，可以帮助你查看自己的 IP 信息，检测网站的可用性，查看 DNS 出口信息，进行网速测试，全球延迟测试，MTR 测试等。", "product2": "这是一个开源项目，你可以点击页面底部的 GitHub 图标查看源代码。", "product3": "最早的时候，这是我的一个用来学习 Vue.js 和学习如何使用 ChatGPT 写代码的练手项目，后来在 GitHub 上获得的星星数量越来越多，于是我进行了大量的重构和添加了许多非常有用的功能，希望你会喜欢。", "meTitle": "关于我", "me1": "我的名字叫阿禅，通常在网上使用 Jason Ng 作为英文名，我并不是一个专业的程序员，但是我喜欢编程，我喜欢开源，我喜欢分享。", "me2": "在大多数时候，我是一名产品经理、一名博客作者、一个摩托车骑士以及一名极客。", "me3": "你可以在下面的链接里找到我更详细的介绍：", "contactTitle": "联系我", "contact": "如果你有任何问题或建议，请通过邮箱联系我： jason[AT]kenengba.com 。", "personal": "个人网站", "blog": "可能吧博客", "retiremoney": "躺平计算器", "twitter": "Twitter", "Sponsor": "赞助"}, "specialthanks": {"Title": "特别鸣谢", "Note1": "感谢以下个人和组织的支持与帮助："}, "changelog": {"Title": "更新日志", "add": "新增", "improve": "优化", "fix": "修复", "versions": [{"version": "v1.0", "date": "Nov 06, 2020", "content": [{"type": "add", "change": "显示用户的 IP 信息"}, {"type": "add", "change": "显示代理前后的 IP 信息"}, {"type": "add", "change": "检查网站可用性"}]}, {"version": "v2.0", "date": "Nov 24, 2023", "content": [{"type": "add", "change": "使用 Vue2 重构了整个项目"}, {"type": "improve", "change": "使用 Bootstrap v5 重构了 UI"}]}, {"version": "v2.1", "date": "Nov 25, 2023", "content": [{"type": "add", "change": "添加了 WebRTC 测试"}, {"type": "improve", "change": "优化了程序的一些逻辑"}]}, {"version": "v2.2", "date": "Nov 26, 2023", "content": [{"type": "add", "change": "添加了 DNS 泄漏测试"}, {"type": "add", "change": "支持在前端显示 Bing 地图"}, {"type": "improve", "change": "多个逻辑优化"}]}, {"version": "v2.3", "date": "Nov 27, 2023 ", "content": [{"type": "add", "change": "添加了黑暗模式"}, {"type": "add", "change": "增加移动版简约模式"}, {"type": "improve", "change": "优化了文件结构"}]}, {"version": "v2.4", "date": "Dec 1, 2023", "content": [{"type": "add", "change": "支持英文语言"}, {"type": "add", "change": "添加信息遮罩功能"}, {"type": "add", "change": "支持 PWA，可以安装到手机和桌面上"}]}, {"version": "v2.5", "date": "Dec 20, 2023", "content": [{"type": "add", "change": "支持快捷键操作"}, {"type": "add", "change": "添加网速测试功能"}, {"type": "improve", "change": "支持使用 Docker 部署"}, {"type": "improve", "change": "支持从多个来源获取 IP 归属地等信息"}]}, {"version": "v3.0", "date": "Jan 28, 2024", "content": [{"type": "add", "change": "使用 Vite + Vue3 重构了整个项目"}, {"type": "add", "change": "增加全球延迟测试功能"}, {"type": "add", "change": "增加 MTR 测试功能"}, {"type": "improve", "change": "多个性能优化和体验优化"}]}, {"version": "v3.1", "date": "Jan 30, 2024", "content": [{"type": "add", "change": "支持新的语言：法语"}, {"type": "add", "change": "添加数据加载中的动画"}, {"type": "improve", "change": "优化了缓存策略"}, {"type": "improve", "change": "调整黑暗模式样式"}, {"type": "fix", "change": "修复了一些小问题"}]}, {"version": "v3.2", "date": "Feb 3, 2024", "content": [{"type": "add", "change": "添加 NAT 类型检测"}, {"type": "improve", "change": "优化 PWA 应用的体验"}, {"type": "improve", "change": "提高后端脚本安全性"}]}, {"version": "v3.3", "date": "Feb 6, 2024", "content": [{"type": "add", "change": "支持使用不同的 IP 地理位置信息源"}, {"type": "improve", "change": "优化连接可用性检测的用户体验"}, {"type": "improve", "change": "优化 UI 体验"}]}, {"version": "v3.4", "date": "Feb 17, 2024", "content": [{"type": "add", "change": "添加 ASN 详细信息查看器"}, {"type": "add", "change": "添加 IP 地址是否为代理的判断"}, {"type": "improve", "change": "优化 IP 信息的展示 UI"}]}, {"version": "v3.5", "date": "Mar 3, 2024", "content": [{"type": "add", "change": "添加规则测试"}, {"type": "improve", "change": "优化了部分逻辑"}]}, {"version": "v3.6", "date": "Mar 10, 2024", "content": [{"type": "add", "change": "添加 DNS 解析功能"}, {"type": "improve", "change": "优化主页结构，高级功能折叠展示"}, {"type": "improve", "change": "优化了程序逻辑"}]}, {"version": "v3.7", "date": "Apr 26, 2024", "content": [{"type": "add", "change": "添加封锁测试功能"}, {"type": "add", "change": "网速测试可以自定义数据包大小"}, {"type": "improve", "change": "优化了部分功能逻辑"}]}, {"version": "v3.8", "date": "May 4, 2024", "content": [{"type": "add", "change": "增加设置面板，可以在本地保存偏好设置"}, {"type": "add", "change": "可以自定义程序在第二次启动时的多个默认行为"}, {"type": "improve", "change": "大量优化了程序的逻辑"}, {"type": "fix", "change": "修复了一些小问题"}]}, {"version": "v3.9", "date": "May 8, 2024", "content": [{"type": "add", "change": "增加 Whois 查询功能"}, {"type": "improve", "change": "优化高级功能的展示与动画效果"}, {"type": "fix", "change": "修复了一些小问题"}]}, {"version": "v4.0", "date": "May 13, 2024", "content": [{"type": "add", "change": "采用 Vue 3 的 Composition API 重构了整个项目"}, {"type": "add", "change": "拆分前端多个模块，提高了程序的可维护性"}, {"type": "add", "change": "增加隐身测试功能（Beta）"}, {"type": "improve", "change": "优化后端程序的依赖结构"}, {"type": "improve", "change": "优化了大量细节"}]}, {"version": "v4.1", "date": "July 11, 2024", "content": [{"type": "add", "change": "添加 MAC 地址查询功能"}, {"type": "improve", "change": "优化了部分程序细节"}]}, {"version": "v4.2", "date": "Sept 25, 2024", "content": [{"type": "add", "change": "添加浏览器信息查询功能"}, {"type": "add", "change": "添加 MaxMind 地理数据库"}, {"type": "improve", "change": "多处体验优化"}]}, {"version": "v4.3", "date": "Oct 30, 2024", "content": [{"type": "add", "change": "添加命令行 API 功能"}, {"type": "improve", "change": "WebRTC 测试可以展示地区信息"}, {"type": "improve", "change": "DNS 泄露测试可以展示地区信息"}]}, {"version": "v4.4", "date": "Dec 7, 2024", "content": [{"type": "add", "change": "语言偏好设置"}, {"type": "improve", "change": "地图展示供应商变更为 Google"}, {"type": "improve", "change": "其它常规优化"}]}, {"version": "v4.5", "date": "Dec 25, 2024", "content": [{"type": "add", "change": "添加安全检查清单"}, {"type": "improve", "change": "其它常规优化"}]}, {"version": "v5.0", "date": "Jan 31, 2025", "content": [{"type": "add", "change": "新增用户系统"}, {"type": "add", "change": "新增用户成就系统"}, {"type": "add", "change": "新增 Lite 版本"}, {"type": "add", "change": "网速测试可以看到数据变化图表"}, {"type": "improve", "change": "代码效率优化"}]}]}}