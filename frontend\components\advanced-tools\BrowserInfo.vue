<template>
    <!-- Browser Info -->
    <div class="browser-info-section my-4">
        <div class="text-secondary">
            <p>{{ t('browserinfo.Note') }}</p>
            <p>{{ t('browserinfo.Note2') }}</p>
        </div>
        <div class="row">
            <div class="col-12 mb-3">
                <div class="card jn-card" :class="{ 'dark-mode dark-mode-border': isDarkMode }">

                    <div class="card-body">
                        <Transition name="slide-fade" mode="out-in">
                            <div id="browserInfoResult" class="row" v-if="checkingStatus === 'finished'">
                                <div class="col-lg-9 col-md-9 col-12 mb-4">
                                    <div class="h-100">
                                        <div class="card-body row"
                                            :class="[isMobile ? 'p-1 border-1 border-bottom' : '']">
                                            <h3 class="mb-4">{{ t('browserinfo.browser.Infos') }} <i
                                                    class="bi bi-person-workspace"></i></h3>
                                            <div class="jn-ua-box w-100">
                                                <div class="alert alert-success jn-ua-box">
                                                    <span class="mb-1 badge text-bg-success">User Agent</span>
                                                    <span><span class="jn-code-font ">{{ userAgent.ua }}</span> <i
                                                            :class="copiedStatus ? 'bi bi-clipboard-check-fill' : 'bi bi-clipboard-plus'"
                                                            @click="copyToClipboard(userAgent.ua)" role="button"
                                                            aria-label="Copy UA"></i></span>
                                                </div>
                                            </div>

                                            <div class="col-lg-4 col-md-4 col-12">
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.browserName') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ userAgent.browser.name }} {{ userAgent.browser.version }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.engineName') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ userAgent.engine.name }} {{ userAgent.engine.version }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.osName') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ userAgent.os.name }} {{ userAgent.os.version }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.language') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.browserLanguage }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.cookieEnabled') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.cookieEnabled?
                                                        t('browserinfo.browser.cookieEnabledTrue'):t('browserinfo.browser.cookieEnabledFalse')
                                                        }}
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="col-lg-4 col-md-4 col-12">
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.deviceVendor') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ userAgent.device.vendor }} {{ userAgent.device.model }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.cpuArchitecture') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ userAgent.device.cpu ? userAgent.device.cpu.architecture :
                                                        'N/A'
                                                        }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.gpu') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ gpu }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.cpuCores') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.cpucores }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.memory') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.memory }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.platform') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.platform }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.timezone') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.timezone }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.screenResolution') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.screenResolution }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.colorDepth') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.colorDepth }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.pixelRatio') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.pixelRatio }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.touchSupport') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.touchSupport }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.onlineStatus') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.onlineStatus }}
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="col-lg-4 col-md-4 col-12">
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.javaEnabled') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.javaEnabled }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.doNotTrack') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.doNotTrack }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.webGL') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.webGL }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.canvas') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.canvas }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.webRTC') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.webRTC }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.localStorage') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.localStorage }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.sessionStorage') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.sessionStorage }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.indexedDB') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.indexedDB }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.webWorkers') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.webWorkers }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.serviceWorkers') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.serviceWorkers }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.webAssembly') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.webAssembly }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.permissions') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.permissions }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.battery') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.battery }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.connection') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.connection }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.plugins') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.plugins }}
                                                    </span>
                                                </div>
                                                <div class="jn-detail">
                                                    <span>
                                                        {{ t('browserinfo.browser.mimeTypes') }}
                                                    </span>
                                                    <span class="jn-con-title card-title mt-1">
                                                        {{ otherInfos.mimeTypes }}
                                                    </span>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-3 col-md-3 col-12 mb-4">
                                    <div class="h-100" :class="{ 
                                    'dark-mode dark-mode-border': isDarkMode,
                                    'card': !isMobile
                                    }">
                                        <div class="card-body" :class="[isMobile ? 'p-1' : '']">
                                            <h3 class="mb-4">{{ t('browserinfo.fingerprint.Infos') }} <i
                                                    class="bi bi-fingerprint"></i></h3>
                                            <div class="jn-ua-box w-100">
                                                <div :class="[isMobile ? 'jn-fp-box-mobile' : '']"
                                                    class="alert alert-primary jn-ua-box">
                                                    <span class="mb-1 badge text-bg-primary">{{
                                                        t('browserinfo.fingerprint.fingerprint') }}</span>
                                                    <span class="jn-code-font ">{{ fingerprint }}</span>
                                                </div>
                                            </div>

                                            <p>{{ t('browserinfo.fingerprint.changeOption') }}</p>

                                            <div class="row g-1 m-1">
                                                <div v-for="(value, key) in excludeOptions" :key="key"
                                                    class="form-check form-switch col-6">
                                                    <input class="form-check-input" type="checkbox" :id="key"
                                                        v-model="excludeOptions[key]" />
                                                    <label class="form-check-label" :for="key">
                                                        {{ t(`browserinfo.options.${key}`) }}
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="alert alert-light opacity-75 mt-4" role="alert">
                                                <i class="bi bi-info-circle"></i> {{
                                                t('browserinfo.fingerprint.browserTips') }}
                                            </div>

                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div v-else class="jn-placeholder ">
                                <span v-if="checkingStatus === 'running'">
                                    <span class="spinner-grow spinner-grow-sm text-success" aria-hidden="true"></span>
                                    <span class="text-success">&nbsp;{{ t('browserinfo.calculating') }}</span>
                                </span>
                                <p v-if="checkingStatus === 'error'" class="text-danger">{{ errorMsg }}</p>
                            </div>
                        </Transition>
                    </div>

                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useMainStore } from '@/store';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const store = useMainStore();
const isDarkMode = computed(() => store.isDarkMode);
const isMobile = computed(() => store.isMobile);

const fingerprint = ref('');
const excludeOptions = ref({
    'audio': true,
    'canvas': true,
    'fonts': true,
    'hardware': true,
    'locales': true,
    'permissions': true,
    'plugins': true,
    'screen': true,
    'system': true,
    'webgl': true,
    'math': true,
});
const errorMsg = ref('');
const checkingStatus = ref('idle');
const copiedStatus = ref(false);

const userAgent = ref('');
const gpu = ref('');
const otherInfos = ref({});

// 获取 GPU 信息
const getGPU = async () => {
    try {
        const { getGPUTier } = await import('detect-gpu');
        const gpuTier = await getGPUTier();
        if (gpuTier && gpuTier.gpu) {
            gpu.value = gpuTier.gpu.toLowerCase().replace(/(^\w|\s\w)/g, m => m.toUpperCase());
        } else {
            gpu.value = 'N/A';
        }
    } catch (error) {
        console.error('Error getting GPU info:', error);
        throw error;
    }
}

// 获取 UA
const getUA = async () => {
    try {
        const { UAParser } = await import('ua-parser-js');
        const parser = new UAParser();
        parser.setUA(parser.getUA());
        userAgent.value = parser.getResult();
    } catch (error) {
        console.error('Error getting user agent:', error);
        throw error;
    }
};

// 获取其他信息
const getOtherBrowserInfo = async () => {
    try {
        // 基础信息
        otherInfos.value.browserLanguage = navigator.language;
        otherInfos.value.cookieEnabled = navigator.cookieEnabled ? '是' : '否';
        otherInfos.value.cpucores = navigator.hardwareConcurrency || 'N/A';

        // 内存信息
        if (navigator.deviceMemory) {
            otherInfos.value.memory = `${navigator.deviceMemory} GB`;
        } else {
            otherInfos.value.memory = 'N/A';
        }

        // 平台信息
        otherInfos.value.platform = navigator.platform || 'N/A';

        // 时区信息
        otherInfos.value.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone || 'N/A';

        // 屏幕信息
        otherInfos.value.screenResolution = `${screen.width} × ${screen.height}`;
        otherInfos.value.colorDepth = `${screen.colorDepth} 位`;
        otherInfos.value.pixelRatio = window.devicePixelRatio || 'N/A';

        // 触摸支持
        otherInfos.value.touchSupport = 'ontouchstart' in window ? '支持' : '不支持';

        // 在线状态
        otherInfos.value.onlineStatus = navigator.onLine ? '在线' : '离线';

        // Java 支持
        try {
            // 调试信息 (可选择性启用)
            // console.log('Java Debug:', { javaEnabledExists: typeof navigator.javaEnabled === 'function' });

            if (typeof navigator.javaEnabled === 'function') {
                const javaEnabled = navigator.javaEnabled();
                otherInfos.value.javaEnabled = javaEnabled ? '启用' : '禁用';
            } else {
                // 现代浏览器通常不支持Java
                otherInfos.value.javaEnabled = '不支持 (现代浏览器)';
            }
        } catch (e) {
            console.error('Java Detection Error:', e);
            otherInfos.value.javaEnabled = '不支持';
        }

        // Do Not Track
        otherInfos.value.doNotTrack = navigator.doNotTrack === '1' ? '启用' : '禁用';

        // WebGL 支持
        let canvas = null;
        let gl = null;
        try {
            canvas = document.createElement('canvas');
            gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            otherInfos.value.webGL = gl ? '支持' : '不支持';
        } catch (e) {
            otherInfos.value.webGL = '不支持';
        }

        // Canvas 指纹
        try {
            if (!canvas) canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            if (ctx) {
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillText('Canvas fingerprint', 2, 2);
                otherInfos.value.canvas = canvas.toDataURL().slice(-8);
            } else {
                otherInfos.value.canvas = 'N/A';
            }
        } catch (e) {
            otherInfos.value.canvas = 'N/A';
        }

        // WebRTC 支持
        otherInfos.value.webRTC = (window.RTCPeerConnection || window.webkitRTCPeerConnection || window.mozRTCPeerConnection) ? '支持' : '不支持';

        // 存储支持
        otherInfos.value.localStorage = typeof(Storage) !== 'undefined' && window.localStorage ? '支持' : '不支持';
        otherInfos.value.sessionStorage = typeof(Storage) !== 'undefined' && window.sessionStorage ? '支持' : '不支持';
        otherInfos.value.indexedDB = window.indexedDB ? '支持' : '不支持';

        // Web Workers 支持
        otherInfos.value.webWorkers = typeof(Worker) !== 'undefined' ? '支持' : '不支持';

        // Service Workers 支持
        otherInfos.value.serviceWorkers = 'serviceWorker' in navigator ? '支持' : '不支持';

        // WebAssembly 支持
        otherInfos.value.webAssembly = typeof WebAssembly === 'object' ? '支持' : '不支持';

        // 权限 API
        otherInfos.value.permissions = 'permissions' in navigator ? '支持' : '不支持';

        // 电池 API
        try {
            if ('getBattery' in navigator) {
                const battery = await navigator.getBattery();
                const rawLevel = battery.level;
                const charging = battery.charging;

                // 调试信息 (可选择性启用)
                // console.log('Battery API Debug:', { rawLevel, charging });

                const chargingText = charging ? '充电中' : '未充电';

                // 检查原始level值是否有效
                if (rawLevel !== undefined && rawLevel !== null && !isNaN(rawLevel)) {
                    const level = Math.round(rawLevel * 100);
                    if (level >= 0 && level <= 100) {
                        otherInfos.value.battery = `${level}% (${chargingText})`;
                    } else {
                        otherInfos.value.battery = `电量异常: ${rawLevel} (${chargingText})`;
                    }
                } else {
                    otherInfos.value.battery = `无法获取电量: ${rawLevel} (${chargingText})`;
                }
            } else {
                otherInfos.value.battery = '不支持';
            }
        } catch (e) {
            console.error('Battery API Error:', e);
            otherInfos.value.battery = '不支持';
        }

        // 网络连接信息
        try {
            if ('connection' in navigator) {
                const conn = navigator.connection;

                // 调试信息
                console.log('Network Connection Debug:', {
                    type: conn.type,
                    effectiveType: conn.effectiveType,
                    downlink: conn.downlink,
                    rtt: conn.rtt
                });

                // 智能检测网络连接类型
                let connectionType = 'unknown';

                // 1. 优先使用实际连接类型（如果可用）
                if (conn.type && conn.type !== 'unknown') {
                    connectionType = conn.type;
                    console.log('Using actual connection type:', connectionType);
                } else {
                    // 2. 如果没有实际类型，使用更智能的推断
                    const downlink = conn.downlink || 0;
                    const rtt = conn.rtt || 0;
                    const effectiveType = conn.effectiveType || '';

                    console.log('Inferring connection type from:', { downlink, rtt, effectiveType });

                    // 检查设备类型和环境
                    const userAgent = navigator.userAgent.toLowerCase();
                    const isMobile = /mobile|android|iphone|ipad|tablet/.test(userAgent);
                    const isDesktop = !isMobile;

                    // 强制WiFi检测逻辑（适用于大多数桌面用户）
                    if (isDesktop) {
                        // 桌面设备：优先假设是WiFi
                        if (downlink >= 100 && rtt <= 10) {
                            connectionType = 'ethernet';
                        } else {
                            // 桌面设备强制显示WiFi（除非明确检测到以太网）
                            connectionType = 'wifi';
                        }
                    } else {
                        // 移动设备：仍然根据effectiveType判断
                        if (effectiveType === '4g' && downlink < 5) {
                            connectionType = 'cellular';
                        } else if (effectiveType === '3g' || effectiveType === '2g' || effectiveType === 'slow-2g') {
                            connectionType = 'cellular';
                        } else {
                            // 移动设备也优先显示WiFi
                            connectionType = 'wifi';
                        }
                    }

                    console.log('Device type:', { isMobile, isDesktop });
                    console.log('Inferred connection type:', connectionType);
                }

                // 格式化连接类型显示
                const typeMap = {
                    'wifi': 'WiFi',
                    'ethernet': '以太网',
                    'cellular': '蜂窝网络',
                    'bluetooth': '蓝牙',
                    'wimax': 'WiMAX',
                    'unknown': '未知连接'
                };

                let displayType = typeMap[connectionType] || connectionType;

                // 特殊处理：如果是蜂窝网络，添加具体的网络制式
                if (connectionType === 'cellular' && conn.effectiveType) {
                    const effectiveTypeMap = {
                        'slow-2g': '慢速2G',
                        '2g': '2G',
                        '3g': '3G',
                        '4g': '4G'
                    };
                    const networkStandard = effectiveTypeMap[conn.effectiveType] || conn.effectiveType;
                    displayType = `蜂窝网络 (${networkStandard})`;
                }

                // 特殊处理：如果推断失败，直接使用effectiveType作为降级
                if (connectionType === 'unknown' && conn.effectiveType) {
                    const fallbackTypeMap = {
                        'slow-2g': '慢速2G',
                        '2g': '2G',
                        '3g': '3G',
                        '4g': '4G'
                    };
                    displayType = fallbackTypeMap[conn.effectiveType] || conn.effectiveType;
                }

                // 添加速度和延迟信息
                const downlink = conn.downlink ? ` ${conn.downlink} Mbps` : '';
                const rtt = conn.rtt ? ` (延迟${conn.rtt}ms)` : '';

                const finalDisplay = `${displayType}${downlink}${rtt}`;
                console.log('Final network display:', finalDisplay);

                otherInfos.value.connection = finalDisplay;
            } else {
                otherInfos.value.connection = 'N/A';
            }
        } catch (e) {
            console.error('Network Connection Error:', e);
            otherInfos.value.connection = 'N/A';
        }

        // 插件信息
        try {
            const pluginCount = navigator.plugins ? navigator.plugins.length : 0;

            // 调试信息 (可选择性启用)
            // console.log('Plugins Debug:', { totalCount: pluginCount, isIncognito: pluginCount === 0 });

            if (pluginCount > 0) {
                const enabledPlugins = [];

                // 获取已启用的插件名称，过滤掉内置插件
                for (let i = 0; i < Math.min(pluginCount, 10); i++) {
                    const plugin = navigator.plugins[i];
                    if (plugin && plugin.name) {
                        // 过滤掉一些内置的PDF查看器
                        const name = plugin.name;
                        if (!name.includes('PDF Viewer') &&
                            !name.includes('Chrome PDF') &&
                            !name.includes('Chromium PDF')) {
                            enabledPlugins.push(name);
                        }
                    }
                }

                if (enabledPlugins.length > 0) {
                    const pluginList = enabledPlugins.slice(0, 3).join(', ');
                    const moreText = enabledPlugins.length > 3 ? ` 等${enabledPlugins.length}个` : '';
                    otherInfos.value.plugins = `${pluginList}${moreText}`;
                } else {
                    // 只有内置插件
                    otherInfos.value.plugins = `仅内置插件 (${pluginCount}个)`;
                }
            } else {
                // 检查是否可能是无痕模式
                const isLikelyIncognito = navigator.plugins !== null && pluginCount === 0;
                if (isLikelyIncognito) {
                    otherInfos.value.plugins = '无插件 (可能是无痕模式)';
                } else {
                    otherInfos.value.plugins = '无插件';
                }
            }
        } catch (e) {
            console.error('Plugins Error:', e);
            otherInfos.value.plugins = 'N/A';
        }

        // MIME 类型
        try {
            if (navigator.mimeTypes && navigator.mimeTypes.length > 0) {
                const mimeCount = navigator.mimeTypes.length;
                const commonTypes = [];

                // 检查常见的MIME类型
                const checkTypes = ['application/pdf', 'application/x-shockwave-flash', 'video/mp4', 'audio/mpeg'];
                for (const type of checkTypes) {
                    if (navigator.mimeTypes[type]) {
                        commonTypes.push(type.split('/')[1]);
                    }
                }

                if (commonTypes.length > 0) {
                    otherInfos.value.mimeTypes = `支持 ${commonTypes.join(', ')} 等${mimeCount}种`;
                } else {
                    otherInfos.value.mimeTypes = `${mimeCount} 种类型`;
                }
            } else {
                otherInfos.value.mimeTypes = '无MIME类型';
            }
        } catch (e) {
            otherInfos.value.mimeTypes = 'N/A';
        }

    } catch (error) {
        console.error('Error getting other browser info:', error);
        throw error;
    }
};

// 获取指纹计算的排除选项
const getExcludeOptions = async () => {
    const results = [];
    const checkOptions = (options, prefix = '') => {
        for (const key in options) {
            const value = options[key];
            const fullPath = prefix ? `${prefix}.${key}` : key;
            if (typeof value === 'object') {
                checkOptions(value, fullPath);
            } else if (!value) {
                results.push(fullPath);
            }
        }
    };

    checkOptions(excludeOptions.value);
    return results;
};

// 获取指纹
const getFingerPrint = async () => {
    fingerprint.value = t('browserinfo.calculating');
    try {
        let excludes = await getExcludeOptions();
        const { getFingerprint, setOption } = await import('@thumbmarkjs/thumbmarkjs');
        setOption('exclude', excludes);
        const getFP = await getFingerprint();
        fingerprint.value = getFP;
    } catch (error) {
        console.error('Error getting fingerprint:', error);
        throw error;
    }
};

// 获取全部
const getAll = async () => {
    try {
        checkingStatus.value = 'running';
        await Promise.all([
            getUA(),
            getFingerPrint(),
            getGPU(),
            getOtherBrowserInfo()
        ]);
        checkingStatus.value = 'finished';
    } catch (error) {
        console.error('Error during checks:', error);
        checkingStatus.value = 'error';
        errorMsg.value = t('browserinfo.calError');
    }
}

// 复制
const copyToClipboard = async (ua) => {
    try {
        await navigator.clipboard.writeText(ua);
        copiedStatus.value = true;
        setTimeout(() => {
            copiedStatus.value = false;
        }, 5000);
    } catch (err) {
        console.error('Copy error:', err);
    }
};

onMounted(() => {
    checkingStatus.value = 'running';
    setTimeout(() => {
        getAll();
    }, 1000);
});

// 监控排除选项
watch(excludeOptions, (newVal, oldVal) => {
    getFingerPrint();
}, { immediate: true, deep: true });

</script>

<style scoped>
.jn-placeholder {
    height: 16pt;
}

.jn-ua-box {
    height: fit-content;
    display: flex;
    flex-direction: column;
}

.jn-ua-box .badge {
    width: fit-content;
}

.jn-code-font {
    font-family: "IntelOne Mono", "Courier New", "Courier", "monospace";
    font-weight: 400;
}

.jn-fp-box-mobile {
    min-height: 80pt;
}

.slide-fade-enter-active {
    transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
    transition: all 0.3s ease-out;
}

.slide-fade-enter-from {
    transform: translateY(60px);
    opacity: 0;
}

.slide-fade-leave-to {
    opacity: 0;
}

.jn-detail {
    display: flex;
    flex-direction: column;
    align-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    margin-bottom: 10pt;
}
</style>