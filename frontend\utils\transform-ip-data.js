// 解析IP数据
function transformDataFromIPapi(data, ipGeoSource, t, mapLanguage) {
    if (data.error) {
        // 确保错误信息是字符串
        const errorMessage = typeof data.error === 'string' ? data.error : JSON.stringify(data.error);
        throw new Error(errorMessage);
    }

    // 调试日志：查看原始数据中的经纬度信息
    console.log(`[transformDataFromIPapi] Processing data for source ${ipGeoSource}:`, {
        latitude: data.latitude,
        longitude: data.longitude,
        lat: data.lat,
        lon: data.lon,
        loc: data.loc
    });

    // 确保所有字段即使缺失也填充 'N/A'，而不是空字符串
    const baseData = {
        // ip 字段通常由 IPDataCards 外部管理，这里不包含
        country_name: data.country_name || 'N/A',
        country_code: data.country_code || 'N/A', 
        
        // --- 核心修改：统一 province 和 region 到 baseData.region ---
        // data.region 是其他API返回的省份字段
        // data.province 是美团API返回的省份字段
        region: data.region || data.province || 'N/A', 
        // -----------------------------------------------------------

        city: data.city || 'N/A',
        // --- 核心修改：统一网络/ISP字段到 baseData.isp ---
        // data.isp 是Workers统一处理后的网络字段
        // data.org 是原始API返回的组织字段
        // data.network 是其他API可能返回的网络字段
        isp: data.isp || data.org || data.network || 'N/A', // 优先使用isp，其次org，再次network，最后N/A
        // -------------------------------------------------

        asn: data.asn || 'N/A', 
        
        // asnlink 逻辑保持不变
        asnlink: data.asn && data.asn !== 'N/A' ? `https://radar.cloudflare.com/${data.asn.replace('AS', '')}` : false,
        // mapUrl 逻辑保持不变，确保经纬度为 'N/A' 时不生成 URL
        // 支持多种经纬度字段名
        latitude: data.latitude || data.lat || (data.loc ? data.loc.split(',')[0] : null) || 'N/A',
        longitude: data.longitude || data.lng || data.lon || (data.loc ? data.loc.split(',')[1] : null) || 'N/A',
        mapUrl: (data.latitude && data.latitude !== 'N/A' && data.longitude && data.longitude !== 'N/A') ? `/api/map?latitude=${data.latitude}&longitude=${data.longitude}&language=${mapLanguage}` : 'N/A',
        mapUrl_dark: (data.latitude && data.latitude !== 'N/A' && data.longitude && data.longitude !== 'N/A') ? `/api/map?latitude=${data.latitude}&longitude=${data.longitude}&language=${mapLanguage}&CanvasMode=Dark` : 'N/A',
        
        // 新增/保持 postal 和 timezone
        postal: data.postal || 'N/A',
        timezone: data.timezone || 'N/A'
    };

    if (ipGeoSource === 0) { // 假设 ipGeoSource === 0 是你的主要 IP 检测源 (wob.IPAPICOM)
        const proxyDetails = extractProxyDetails(data.proxyDetect, t); 
        return {
            ...baseData,
            ...proxyDetails,
        };
    }

    return baseData;
};

// 解析代理数据 (保持不变)
function extractProxyDetails(proxyDetect = {}, t) {
    const isProxy = determineIsProxy(proxyDetect, t);
    const type = determineType(proxyDetect, t);
    const qualityScore = proxyDetect.risk === 'unknown' ? 'N/A' : (proxyDetect.risk === 'sign_in_required' ? 'sign_in_required' : (typeof proxyDetect.risk === 'number' ? (100 - proxyDetect.risk) : 'N/A'));
    const proxyProtocol = determineProtocol(proxyDetect, t);
    const proxyOperator = proxyDetect.operator || 'N/A'; 

    return { isProxy, type, qualityScore, proxyProtocol, proxyOperator };
}

// 判断是否代理 (保持不变)
function determineIsProxy(proxyDetect, t) {
    if (proxyDetect.proxy === 'yes' && proxyDetect.protocol !== 'unknown') {
        return t('ipInfos.proxyDetect.yes');
    } else if (proxyDetect.proxy === 'yes') {
        return t('ipInfos.proxyDetect.maybe');
    } else if (proxyDetect.proxy === 'no') {
        return t('ipInfos.proxyDetect.no');
    } else if (proxyDetect.proxy === 'sign_in_required') {
        return 'sign_in_required';
    } else {
        return t('ipInfos.proxyDetect.unknownProxyType');
    }
}

// 判断代理类型 (保持不变)
function determineType(proxyDetect, t) {
    switch (proxyDetect.type) {
        case 'Business':
            return t('ipInfos.proxyDetect.type.Business');
        case 'Residential':
            return t('ipInfos.proxyDetect.type.Residential');
        case 'Wireless':
            return t('ipInfos.proxyDetect.type.Wireless');
        case 'Hosting':
            return t('ipInfos.proxyDetect.type.Hosting');
        case 'VPN':
            if (proxyDetect.protocol === 'unknown') {
                return t('ipInfos.proxyDetect.type.Hosting');
            }
        default:
            return proxyDetect.type || t('ipInfos.proxyDetect.type.unknownType');
    }
}

// 判断代理协议 (保持不变)
function determineProtocol(proxyDetect, t) {
    if (proxyDetect.protocol === 'unknown' || !proxyDetect.protocol) {
        return t('ipInfos.proxyDetect.unknownProtocol');
    } else {
        return proxyDetect.protocol;
    }
}

export { transformDataFromIPapi, extractProxyDetails };
