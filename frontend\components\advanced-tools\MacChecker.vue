<template>
  <div class="jn-card">
    <div class="jn-card-header">
      <h2 class="jn-card-title">
        <i class="fas fa-network-wired"></i>
        {{ t('macchecker.Title') }}
      </h2>
    </div>
    <div class="jn-card-body">
      <div class="jn-alert jn-alert-info">
        <i class="fas fa-info-circle"></i>
        {{ t('macchecker.Note') }}
      </div>

      <div class="jn-form-group">
        <label for="macInput" class="jn-label">{{ t('macchecker.Note2') }}</label>
        <div class="jn-input-group">
          <input
            id="macInput"
            v-model="macInput"
            type="text"
            class="jn-input"
            :placeholder="t('macchecker.Placeholder')"
            @keyup.enter="handleQuery"
          />
          <button
            class="jn-btn jn-btn-primary"
            :disabled="macCheckStatus === 'running'"
            @click="handleQuery"
          >
            <i v-if="macCheckStatus === 'running'" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-search"></i>
            {{ t('macchecker.Run') }}
          </button>
        </div>
      </div>

      <div class="jn-placeholder">
        <p v-if="errorMsg" class="text-danger">{{ errorMsg }}</p>
      </div>

      <!-- 结果显示区域 -->
      <div v-if="macCheckResult && macCheckResult.success" class="jn-result-section">
        <div class="jn-result-grid">
          <div class="jn-result-item">
            <span class="jn-result-label">{{ t('macchecker.macPrefix') }}:</span>
            <span class="jn-result-value">{{ macCheckResult.macPrefix }}</span>
          </div>
          <div class="jn-result-item">
            <span class="jn-result-label">{{ t('macchecker.company') }}:</span>
            <span class="jn-result-value">{{ macCheckResult.company }}</span>
          </div>
          <div class="jn-result-item">
            <span class="jn-result-label">{{ t('macchecker.country') }}:</span>
            <span class="jn-result-value">{{ macCheckResult.country }}</span>
          </div>
          <div class="jn-result-item">
            <span class="jn-result-label">{{ t('macchecker.type') }}:</span>
            <span class="jn-result-value">{{ macCheckResult.type || 'N/A' }}</span>
          </div>
          <div class="jn-result-item">
            <span class="jn-result-label">{{ t('macchecker.address') }}:</span>
            <span class="jn-result-value">{{ macCheckResult.address }}</span>
          </div>
          <div class="jn-result-item">
            <span class="jn-result-label">{{ t('macchecker.blockStart') }}:</span>
            <span class="jn-result-value">{{ macCheckResult.blockStart }}</span>
          </div>
          <div class="jn-result-item">
            <span class="jn-result-label">{{ t('macchecker.blockEnd') }}:</span>
            <span class="jn-result-value">{{ macCheckResult.blockEnd }}</span>
          </div>
          <div class="jn-result-item">
            <span class="jn-result-label">{{ t('macchecker.blockSize') }}:</span>
            <span class="jn-result-value">{{ macCheckResult.blockSize }}</span>
          </div>
          <div class="jn-result-item">
            <span class="jn-result-label">{{ t('macchecker.blockType') }}:</span>
            <span class="jn-result-value">{{ macCheckResult.blockType }}</span>
          </div>
          <div class="jn-result-item">
            <span class="jn-result-label">{{ t('macchecker.isMulticast') }}:</span>
            <span class="jn-result-value">{{ macCheckResult.isMulticast ? t('macchecker.yes') : t('macchecker.no') }}</span>
          </div>
          <div class="jn-result-item">
            <span class="jn-result-label">{{ t('macchecker.isUnicast') }}:</span>
            <span class="jn-result-value">{{ macCheckResult.isUnicast ? t('macchecker.yes') : t('macchecker.no') }}</span>
          </div>
          <div class="jn-result-item">
            <span class="jn-result-label">{{ t('macchecker.isGlobal') }}:</span>
            <span class="jn-result-value">{{ macCheckResult.isGlobal ? t('macchecker.yes') : t('macchecker.no') }}</span>
          </div>
          <div class="jn-result-item">
            <span class="jn-result-label">{{ t('macchecker.isLocal') }}:</span>
            <span class="jn-result-value">{{ macCheckResult.isLocal ? t('macchecker.yes') : t('macchecker.no') }}</span>
          </div>
          <div class="jn-result-item">
            <span class="jn-result-label">{{ t('macchecker.property') }}:</span>
            <span class="jn-result-value">{{ t('macchecker.value') }}</span>
          </div>
          <div class="jn-result-item">
            <span class="jn-result-label">{{ t('macchecker.manufacturer') }}:</span>
            <span class="jn-result-value">{{ macCheckResult.company }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// 响应式数据
const macInput = ref('');
const macCheckResult = ref(null);
const macCheckStatus = ref('idle'); // 'idle', 'running'
const errorMsg = ref('');
const initialCheckDone = ref(false);

// 页面加载时的初始化
onMounted(() => {
  console.log('MacChecker component mounted');
});

// 输入验证函数
const validateInput = (input) => {
  if (!input || input.trim() === '') {
    return { isValid: false, error: t('macchecker.inputRequired') };
  }

  // 移除所有分隔符并转换为大写
  const cleanInput = input.replace(/[:-]/g, '').toUpperCase();

  // 检查是否只包含十六进制字符
  if (!/^[0-9A-F]+$/.test(cleanInput)) {
    return { isValid: false, error: t('macchecker.invalidMacCharacters') };
  }

  // 检查长度（MAC地址应该是12个十六进制字符）
  if (cleanInput.length !== 12) {
    return { isValid: false, error: t('macchecker.invalidMacLength') };
  }

  return { isValid: true, cleanInput };
};

// 处理查询请求
const handleQuery = async () => {
  const input = macInput.value.trim();
  
  // 验证输入
  const validation = validateInput(input);
  if (!validation.isValid) {
    errorMsg.value = validation.error;
    macCheckResult.value = null;
    return;
  }

  // 执行查询
  await getMacInfo(validation.cleanInput);
};

// 获取 MAC 信息
const getMacInfo = async (query) => {
  macCheckStatus.value = 'running';
  try {
    // 将纯十六进制字符串转换为标准MAC地址格式
    const formatMacAddress = (hexString) => {
      // 确保是12位十六进制字符
      const paddedHex = hexString.padEnd(12, '0').substring(0, 12);
      // 添加冒号分隔符
      return paddedHex.match(/.{2}/g).join(':').toUpperCase();
    };

    const formattedMac = formatMacAddress(query);
    console.log(`Formatted MAC address: ${query} -> ${formattedMac}`);

    // 修正 API URL 构造：使用模板字面量
    const response = await fetch(`/api/tools?tool=mac&mac=${encodeURIComponent(formattedMac)}`);

    if (!response.ok) {
      let fetchErrorMsg = `Network response was not ok (${response.status} ${response.statusText})`;
      try {
        const errorText = await response.text();
        fetchErrorMsg += ` - ${errorText.substring(0, 200)}`; // 截取部分错误信息
      } catch (e) { /* ignore */ }
      throw new Error(fetchErrorMsg);
    }

    const data = await response.json();
    console.log("API 原始响应:", data); // 调试输出

    // 检查是否有错误信息
    if (data.error) {
      macCheckResult.value = { success: false, found: false };
      errorMsg.value = data.error;
      return;
    }

    // 检查是否找到MAC地址信息 (后端返回found字段)
    if (data.found) {
      // MAC 地址找到，映射数据
      macCheckResult.value = {
        success: true,
        found: true,
        macPrefix: data.oui || 'N/A',
        company: data.company || 'N/A',
        country: data.country || 'N/A',
        type: data.type || 'N/A',
        address: 'N/A', // 后端没有提供地址信息
        blockStart: 'N/A', // 后端没有提供区块信息
        blockEnd: 'N/A',
        blockSize: 'N/A',
        blockType: 'N/A',
        isMulticast: false, // 后端没有提供，默认false
        isUnicast: true, // 大多数MAC地址是单播
        isLocal: false, // 后端没有提供，默认false
        isGlobal: true, // 大多数MAC地址是全球唯一
        isRand: false, // 后端没有提供，默认false
        isPrivate: false, // 后端没有提供，默认false
        source: data.source || 'Unknown',
        lastUpdate: 'N/A'
      };
      errorMsg.value = ''; // 清空错误信息
    } else {
      // MAC 地址未找到
      macCheckResult.value = {
        success: true, // API 请求本身是成功的
        found: false, // 但 MAC 未找到
        macPrefix: data.oui || 'N/A',
        company: data.company || 'Unknown Vendor',
        country: data.country || 'Unknown',
        type: data.type || 'Unknown',
        address: 'N/A',
        blockStart: 'N/A',
        blockEnd: 'N/A',
        blockSize: 'N/A',
        blockType: 'N/A',
        isMulticast: false,
        isUnicast: false,
        isLocal: false,
        isGlobal: false,
        isRand: false,
        isPrivate: false,
        source: data.source || 'Unknown',
        lastUpdate: 'N/A'
      };
      errorMsg.value = data.message || t('macchecker.macNotFound');
    }

  } catch (error) {
    console.error('Error fetching MAC results:', error);
    macCheckStatus.value = 'idle';
    errorMsg.value = t('macchecker.fetchError') + (error.message ? `: ${error.message.substring(0, 200)}` : '');
    macCheckResult.value = { success: false, found: false }; // 确保在错误时也设置状态
  } finally {
    macCheckStatus.value = 'idle';
    initialCheckDone.value = true; // 标记查询已完成
  }
};

</script>

<style scoped>
.jn-result-section {
  margin-top: 1rem;
  padding: 1rem;
  background-color: var(--jn-color-bg-secondary);
  border-radius: 8px;
}

.jn-result-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 0.75rem;
}

.jn-result-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem;
  background-color: var(--jn-color-bg-primary);
  border-radius: 4px;
  border: 1px solid var(--jn-color-border);
}

.jn-result-label {
  font-weight: 600;
  color: var(--jn-color-text-secondary);
}

.jn-result-value {
  color: var(--jn-color-text-primary);
  font-family: 'Courier New', monospace;
}

.text-danger {
  color: #dc3545;
  font-weight: 500;
  margin: 0.5rem 0;
}

.jn-placeholder {
  min-height: 1.5rem;
}
</style>
