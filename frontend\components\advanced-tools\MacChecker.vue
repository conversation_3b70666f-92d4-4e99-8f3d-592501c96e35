<template>
  <!-- <PERSON> Checker -->
  <div class="mac-checker-section my-4">
    <div class="text-secondary">
      <p>{{ t('macchecker.Note') }}</p>
    </div>
    <div class="row">
      <div class="col-12 mb-3">
        <div class="card jn-card" :class="{ 'dark-mode dark-mode-border': isDarkMode }">
          <div class="card-body">
            <div class="col-12 col-md-auto">
              <label for="queryMAC" class="col-form-label">{{ t('macchecker.Note2') }}</label>
            </div>

            <div class="input-group mb-2 mt-2 ">
              <input type="text" class="form-control" :class="{ 'dark-mode': isDarkMode }"
                     :disabled="macCheckStatus === 'running'" :placeholder="t('macchecker.Placeholder')"
                     v-model="queryMAC" @keyup.enter="onSubmit" name="queryMAC" id="queryMAC" data-1p-ignore>

              <button class="btn btn-primary" @click="onSubmit"
                      :disabled="macCheckStatus === 'running' || !queryMAC">
                <span v-if="macCheckStatus !== 'running'">
                  {{
                                    t('macchecker.Run')
                  }}
                </span>
                <span v-else class="spinner-grow spinner-grow-sm" aria-hidden="true"></span>
              </button>

            </div>
            <div class="jn-placeholder">
              <p v-if="errorMsg" class="text-danger">{{ errorMsg }}</p>
            </div>

            <!-- Result Display -->
            <!-- 仅当macCheckResult.success为true（API请求成功）且macCheckResult.found为true（MAC地址找到）时显示详细结果 -->
            <div id="macCheckResult" class="row" v-if="macCheckResult.success && macCheckResult.found">
              <div class="col-lg-8 col-md-8 col-12 mb-4">
                <div class="card h-100" :class="{ 'dark-mode dark-mode-border': isDarkMode }">
                  <div class="card-body row">
                    <h3 class="mb-4">{{ t('macchecker.manufacturer') }}</h3>
                    <div class="col-lg-6 col-md-6 col-12">
                      <div class="jn-detail" v-for="item in leftItems" :key="item.key">
                        <span>
                          {{ t(`macchecker.${item.key}`) }}
                        </span>
                        <span class="jn-con-title card-title mt-1">
                          {{ macCheckResult[item.key] }}
                        </span>
                      </div>
                    </div>

                    <div class="col-lg-6 col-md-6 col-12">
                      <div class="jn-detail">
                        <span>
                          {{ t('macchecker.company') }}
                        </span>
                        <span class="jn-con-title card-title mt-1">
                          {{ macCheckResult.company }}
                        </span>
                      </div>
                      <div v-if="macCheckResult.country && macCheckResult.country !== 'N/A'" class="jn-detail">
                        <span>
                          {{ t('macchecker.country') }}
                        </span>
                        <span class="jn-con-title card-title mt-1">
                          <span :class="'jn-fl fi fi-' + macCheckResult.country.toLowerCase()"></span>
                          {{ getCountryName(macCheckResult.country, lang) }}
                        </span>
                      </div>
                      <div class="jn-detail">
                        <span>
                          {{ t('macchecker.address') }}
                        </span>
                        <span class="jn-con-title card-title mt-1">
                          {{ macCheckResult.address }}
                        </span>
                      </div>
                      <div v-if="macCheckResult.lastUpdate && macCheckResult.lastUpdate !== 'N/A'" class="jn-detail">
                        <span>
                          {{ t('macchecker.lastUpdate') }}
                        </span>
                        <span class="jn-con-title card-title mt-1">
                          {{ macCheckResult.lastUpdate }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-lg-4 col-md-4 col-12 mb-4">
                <div class="card h-100" :class="{ 'dark-mode dark-mode-border': isDarkMode}">
                  <div class="card-body">
                    <h3 class="mb-4">{{ t('macchecker.property') }}</h3>
                    <div class="table-responsive text-nowrap">
                      <table class="table table-hover" :class="{ 'table-dark': isDarkMode }">
                        <thead>
                          <tr>
                            <th scope="col">{{ t('macchecker.property') }}</th>
                            <th scope="col">{{ t('macchecker.value') }}</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="item in tableItems" :key="item.key">
                            <td>{{ t(`macchecker.${item.key}`) }}</td>
                            <td>
                              <i class="bi"
                                 :class="macCheckResult[item.key] ? 'bi-check-circle-fill text-success' : 'bi-x-circle-fill text-secondary'"></i>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 如果API请求成功但MAC未找到，或者API请求本身失败但有macCheckResult.success为false -->
            <div v-else-if="macCheckStatus === 'idle' && macCheckResult.success === false && initialCheckDone">
              <p class="text-secondary">{{ t('macchecker.macNotFound') }}</p>
            </div>
            <!-- 如果API请求成功但MAC未找到，则显示简单的未找到信息 -->
            <div v-else-if="macCheckStatus === 'idle' && macCheckResult.success === true && macCheckResult.found === false && initialCheckDone">
              <p class="text-secondary">{{ t('macchecker.macNotFound') }}</p>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { useMainStore } from '@/store';
  import { useI18n } from 'vue-i18n';
  import { trackEvent } from '@/utils/use-analytics';
  import getCountryName from '@/utils/country-name.js'; // 确保路径正确

  const { t } = useI18n();

  const store = useMainStore();
  const isDarkMode = computed(() => store.isDarkMode);
  // const isMobile = computed(() => store.isMobile); // 未使用
  const lang = computed(() => store.lang);

  const macCheckResult = ref({});
  const macCheckStatus = ref("idle"); // 'idle', 'running'
  const queryMAC = ref('');
  const errorMsg = ref('');
  const initialCheckDone = ref(false); // 新增，用于标记是否已完成首次查询

  const leftItems = computed(() => {
    return [
      { key: 'macPrefix' },
      { key: 'blockStart' },
      { key: 'blockEnd' },
      { key: 'blockSize' },
      { key: 'blockType' }
    ];
  });

  const tableItems = computed(() => {
    return [
      { key: 'isRand' },         // 不在API响应中，将默认false
      { key: 'isPrivate' },      // 不在API响应中，将默认false
      { key: 'isMulticast' },
      { key: 'isUnicast' },
      { key: 'isLocal' },        // 映射自 isLocalAdministered
      { key: 'isGlobal' }        // 映射自 isUniversallyAdministered
    ];
  });

  // 检查 MAC 是否有效
  const validateInput = (input) => {
    if (!input || typeof input !== 'string' || input.trim() === '') {
      errorMsg.value = t('macchecker.inputRequired'); // 新增 i18n 键
      return null;
    }
    // 清理所有的分隔符和空格，只保留十六进制字符
    const cleanInput = input.replace(/[^0-9A-Fa-f]/g, ''); // 移除所有非十六进制字符，包括 $

    // 检查是否只包含十六进制字符
    if (!/^[0-9A-Fa-f]+$/.test(cleanInput)) {
      errorMsg.value = t('macchecker.invalidMacCharacters'); // 新增 i18n 键
      return null;
    }

    // 检查长度：MAC地址通常是6字节（12个十六进制字符）或部分前缀（至少6个字符）
    if (cleanInput.length < 6 || cleanInput.length > 12) {
      errorMsg.value = t('macchecker.invalidMacLength'); // 新增 i18n 键
      return null;
    }

    return cleanInput;
  };

  // 提交查询
  const onSubmit = () => {
    trackEvent('Section', 'StartClick', 'MACChecker');
    errorMsg.value = '';
    macCheckResult.value = {}; // 清空之前的查询结果
    initialCheckDone.value = false; // 重置查询完成状态

    const query = validateInput(queryMAC.value);
    if (query) {
      getMacInfo(query);
    }
  };

  // 获取 MAC 信息
  const getMacInfo = async (query) => {
    macCheckStatus.value = 'running';
    try {
      // 修正 API URL 构造：使用模板字面量
      const response = await fetch(`/api/tools?tool=mac&mac=${encodeURIComponent(query)}`);

      if (!response.ok) {
        let fetchErrorMsg = `Network response was not ok (${response.status} ${response.statusText})`;
        try {
          const errorText = await response.text();
          fetchErrorMsg += ` - ${errorText.substring(0, 200)}`; // 截取部分错误信息
        } catch (e) { /* ignore */ }
        throw new Error(fetchErrorMsg);
      }

      const data = await response.json();
      console.log("API 原始响应:", data); // 调试输出

      if (data.success) {
        if (data.found) {
          // MAC 地址找到，映射数据
          macCheckResult.value = {
            success: true,
            found: true,
            macPrefix: data.macPrefix || 'N/A',
            company: data.company || 'N/A',
            country: data.country || 'N/A',
            address: data.address || 'N/A',
            blockStart: data.blockStart || 'N/A',
            blockEnd: data.blockEnd || 'N/A',
            blockSize: data.blockSize || 'N/A',
            blockType: data.blockType || 'N/A',
            isMulticast: data.isMulticast || false,
            isUnicast: data.isUnicast || false,
            isLocal: data.isLocalAdministered || false, // 映射 API 的 isLocalAdministered
            isGlobal: data.isUniversallyAdministered || false, // 映射 API 的 isUniversallyAdministered
            isRand: false, // API响应中没有，默认false
            isPrivate: false, // API响应中没有，默认false
            lastUpdate: data.lastUpdate || 'N/A'
          };
        } else {
          // MAC 地址未找到，API 仍然返回 success: true
          macCheckResult.value = {
            success: true, // API 请求本身是成功的
            found: false, // 但 MAC 未找到
            macPrefix: t('macchecker.notFound'), // 显示“未找到”
            company: t('macchecker.notFound'),
            country: 'N/A',
            address: t('macchecker.notFound'),
            blockStart: 'N/A',
            blockEnd: 'N/A',
            blockSize: 'N/A',
            blockType: 'N/A',
            isMulticast: false,
            isUnicast: false,
            isLocal: false,
            isGlobal: false,
            isRand: false,
            isPrivate: false,
            lastUpdate: 'N/A'
          };
          errorMsg.value = t('macchecker.macNotFound'); // 提示用户MAC未找到
        }
      } else {
        // API 返回 success: false，可能包含错误信息
        macCheckResult.value = { success: false, found: false };
        errorMsg.value = data.error || t('macchecker.unknownError'); // 使用 API 的错误信息，或通用错误
      }

    } catch (error) {
      console.error('Error fetching MAC results:', error);
      macCheckStatus.value = 'idle';
      errorMsg.value = t('macchecker.fetchError') + (error.message ? `: ${error.message.substring(0, 200)}` : '');
      macCheckResult.value = { success: false, found: false }; // 确保在错误时也设置状态
    } finally {
      macCheckStatus.value = 'idle';
      initialCheckDone.value = true; // 标记查询已完成
    }
  };

</script>

<style scoped>
  .jn-placeholder {
    min-height: 1.5rem; /* 使用rem代替pt，更灵活 */
    display: flex;
    align-items: center;
  }

  .jn-detail {
    display: flex;
    flex-direction: column;
    align-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    margin-bottom: 10pt;
  }
</style>
