// 合并的工具类API
// import { refererMiddleware } from '../middleware/referer-middleware.js';
// import { spawn } from 'child_process';
// import os from 'os';

// 简化的referer检查中间件
const refererMiddleware = (req, res, next) => {
  // 在Vercel环境中简化referer检查
  next();
};

// DNS解析器 - 使用多个公共DNS服务
const dnsResolverHandler = async (req, res) => {
  try {
    const { hostname, type = 'A' } = req.query;

    if (!hostname) {
      return res.status(400).json({ error: 'Hostname parameter is required' });
    }

    // 使用多个DNS服务进行查询
    const dnsProviders = [
      { name: 'Cloudflare', url: `https://cloudflare-dns.com/dns-query?name=${hostname}&type=${type}` },
      { name: 'Google', url: `https://dns.google/resolve?name=${hostname}&type=${type}` },
      { name: 'Quad9', url: `https://dns.quad9.net:5053/dns-query?name=${hostname}&type=${type}` }
    ];

    const results = {};

    // 并行查询所有DNS服务
    const promises = dnsProviders.map(async (provider) => {
      try {
        const response = await fetch(provider.url, {
          headers: {
            'Accept': 'application/dns-json'
          },
          timeout: 5000
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();

        // 处理DNS响应
        if (data.Answer && data.Answer.length > 0) {
          const addresses = data.Answer
            .filter(answer => answer.type === getTypeNumber(type))
            .map(answer => answer.data);

          results[provider.name] = {
            addresses: addresses,
            status: data.Status || 0
          };
        } else {
          results[provider.name] = {
            addresses: [],
            status: data.Status || 3,
            error: data.Status === 3 ? 'NXDOMAIN' : 'NODATA'
          };
        }
      } catch (error) {
        results[provider.name] = {
          addresses: [],
          error: error.message || 'Query failed'
        };
      }
    });

    await Promise.all(promises);

    res.json({
      hostname,
      type,
      results
    });

  } catch (error) {
    console.error('Error in DNS resolver:', error);
    res.status(500).json({ error: 'DNS resolution failed', message: error.message });
  }
};

// DNS记录类型到数字的映射
const getTypeNumber = (type) => {
  const typeMap = {
    'A': 1,
    'AAAA': 28,
    'CNAME': 5,
    'MX': 15,
    'NS': 2,
    'TXT': 16
  };
  return typeMap[type] || 1;
};

// Whois查询 - 简化版本用于Vercel
const whoisHandler = async (req, res) => {
  try {
    const { domain } = req.query;

    if (!domain) {
      return res.status(400).json({ error: 'Domain parameter is required' });
    }

    // 在Vercel环境中，系统命令可能不可用，返回模拟响应
    res.json({
      domain,
      result: `Whois lookup for ${domain} - Feature not available in serverless environment`,
      note: 'This feature requires a full server environment'
    });

  } catch (error) {
    console.error('Error in Whois lookup:', error);
    res.status(500).json({ error: 'Whois lookup failed' });
  }
};

// MAC地址检查器 - 使用外部API服务
const macCheckerHandler = async (req, res) => {
  try {
    const { mac } = req.query;

    if (!mac) {
      return res.status(400).json({ error: 'MAC address parameter is required' });
    }

    // 使用外部API进行MAC地址查询
    try {
      const response = await fetch(`https://myip.wobys.dpdns.org/api/macchecker?mac=${encodeURIComponent(mac)}`, {
        timeout: 10000 // 10秒超时
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      res.json(data);
    } catch (error) {
      console.error('External MAC API error:', error);

      // 简单的MAC地址格式验证作为备用
      const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
      if (!macRegex.test(mac)) {
        return res.status(400).json({ error: 'Invalid MAC address format' });
      }

      // 提取OUI (前3个字节)
      const oui = mac.replace(/[:-]/g, '').substring(0, 6).toUpperCase();

      // 返回基本信息作为备用
      res.json({
        mac: mac,
        oui: oui,
        vendor: 'Unknown - External service unavailable',
        message: 'Using fallback MAC address analysis'
      });
    }

  } catch (error) {
    console.error('Error in MAC checker:', error);
    res.status(500).json({ error: 'MAC address check failed', message: error.message });
  }
};

// Ping测试 - 使用外部API服务
const pingTestHandler = async (req, res) => {
  try {
    const { ip } = req.query;

    if (!ip) {
      return res.status(400).json({ error: 'IP parameter is required' });
    }

    // 使用外部API进行ping测试
    try {
      const response = await fetch(`https://myip.wobys.dpdns.org/api/ping?ip=${encodeURIComponent(ip)}`, {
        timeout: 30000 // 30秒超时
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      res.json(data);
    } catch (error) {
      console.error('External ping API error:', error);
      // 返回模拟数据作为备用
      res.json({
        ip,
        results: [
          { country: '美国', min: 150, max: 200, avg: 175, sent: 4, received: 4, loss: 0 },
          { country: '日本', min: 80, max: 120, avg: 100, sent: 4, received: 4, loss: 0 },
          { country: '新加坡', min: 60, max: 100, avg: 80, sent: 4, received: 4, loss: 0 },
          { country: '德国', min: 200, max: 250, avg: 225, sent: 4, received: 4, loss: 0 }
        ],
        note: 'Using fallback data - external service unavailable'
      });
    }

  } catch (error) {
    console.error('Error in ping test:', error);
    res.status(500).json({ error: 'Ping test failed', message: error.message });
  }
};

// MTR测试 - 简化版本用于Vercel
const mtrTestHandler = async (req, res) => {
  try {
    const { host, count = 10 } = req.query;

    if (!host) {
      return res.status(400).json({ error: 'Host parameter is required' });
    }

    // 在Vercel环境中，系统命令可能不可用，返回模拟响应
    res.json({
      host,
      count,
      exitCode: 0,
      output: `MTR test for ${host} (${count} hops) - Feature not available in serverless environment`,
      error: '',
      note: 'This feature requires a full server environment'
    });

  } catch (error) {
    console.error('Error in MTR test:', error);
    res.status(500).json({ error: 'MTR test failed' });
  }
};

// 主处理函数
export default async function handler(req, res) {
  refererMiddleware(req, res, async () => {
    const { tool } = req.query;
    
    switch (tool) {
      case 'dns':
        return dnsResolverHandler(req, res);
      case 'whois':
        return whoisHandler(req, res);
      case 'mac':
        return macCheckerHandler(req, res);
      case 'ping':
        return pingTestHandler(req, res);
      case 'mtr':
        return mtrTestHandler(req, res);
      default:
        res.status(400).json({ error: 'Invalid tool parameter' });
    }
  });
}
