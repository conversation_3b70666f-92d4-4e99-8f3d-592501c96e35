<template>
  <div class="ip-location-map-container">
    <!-- 地图标题 -->
    <div class="map-header mb-2">
      <h6 class="map-title mb-1">
        <i class="bi bi-geo-alt-fill me-2"></i>IP地理位置
      </h6>
      <small class="text-muted">{{ locationText }}</small>
    </div>

    <!-- 展开/收起按钮 -->
    <div class="text-center mb-2">
      <button
        class="btn btn-sm btn-outline-secondary map-toggle-btn"
        type="button"
        data-bs-toggle="collapse"
        :data-bs-target="`#mapCollapse-${card.id}`"
        :aria-expanded="isMapExpanded"
        :aria-controls="`mapCollapse-${card.id}`"
        @click="toggleMapExpanded"
      >
        <i class="bi me-1" :class="isMapExpanded ? 'bi-chevron-up' : 'bi-chevron-down'"></i>
        {{ isMapExpanded ? '收起' : '展开' }}
      </button>
    </div>

    <!-- 可折叠的地图容器 -->
    <div class="collapse" :id="`mapCollapse-${card.id}`">
      <div class="map-wrapper" :class="{ 'map-loading': isLoading }">
        <div v-if="isLoading" class="map-loading-overlay">
          <div class="spinner-border spinner-border-sm me-2" role="status"></div>
          <span>加载地图中...</span>
        </div>

        <!-- 地图容器 - 总是渲染，但可能隐藏 -->
        <div
          class="map-container"
          ref="mapContainer"
          :id="mapId"
          :style="{ display: hasValidLocation ? 'block' : 'none' }"
        >
          <!-- 地图将通过原生Leaflet API渲染到这里 -->
        </div>

        <!-- 无位置信息时的提示 -->
        <div v-if="!hasValidLocation && !isLoading" class="no-location-message">
          <i class="bi bi-geo-alt text-muted me-2"></i>
          <span class="text-muted">{{ noLocationText }}</span>
        </div>

        <!-- 地图控制按钮 -->
        <div v-if="hasValidLocation" class="map-controls mt-2">
          <button
            class="btn btn-sm btn-outline-primary me-2"
            @click="centerMap"
            :disabled="isLoading"
          >
            <i class="bi bi-crosshair me-1"></i>居中
          </button>
          <button
            class="btn btn-sm btn-outline-secondary me-2"
            @click="toggleMapType"
            :disabled="isLoading"
          >
            <i class="bi bi-layers me-1"></i>{{ currentMapType }}
          </button>
          <button
            class="btn btn-sm btn-outline-info"
            @click="openInExternalMap"
            :disabled="isLoading"
          >
            <i class="bi bi-box-arrow-up-right me-1"></i>外部地图
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';

const props = defineProps({
  card: {
    type: Object,
    required: true
  }
});

// 地图类型配置 - 使用国内外通用的地图服务
const mapTypes = [
  {
    name: '高德',
    url: 'https://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
    attribution: '© 高德地图'
  },
  {
    name: '卫星',
    url: 'https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
    attribution: '© 高德地图'
  },
  {
    name: 'CartoDB',
    url: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
    attribution: '© OpenStreetMap © CartoDB',
    subdomains: ['a', 'b', 'c', 'd']
  },
  {
    name: 'OSM',
    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    attribution: '© OpenStreetMap contributors',
    subdomains: ['a', 'b', 'c']
  }
];

// 响应式数据
const mapContainer = ref(null);
const isLoading = ref(true);
const zoom = ref(10);
const currentMapTypeIndex = ref(0);
const isMapExpanded = ref(false); // 地图默认折叠
const mapId = computed(() => `map-${Math.random().toString(36).substr(2, 9)}`);

// Leaflet实例
let map = null;
let marker = null;
let circle = null;
let tileLayer = null;

// 计算属性
const hasValidLocation = computed(() => {
  const lat = parseFloat(props.card.latitude);
  const lng = parseFloat(props.card.longitude);
  return !isNaN(lat) && !isNaN(lng) && lat !== 0 && lng !== 0;
});

const center = computed(() => {
  if (!hasValidLocation.value) return [0, 0];
  return [parseFloat(props.card.latitude), parseFloat(props.card.longitude)];
});

const locationText = computed(() => {
  if (!hasValidLocation.value) return '位置信息不可用';
  
  const parts = [];
  if (props.card.country_name) parts.push(props.card.country_name);
  if (props.card.region_name) parts.push(props.card.region_name);
  if (props.card.city) parts.push(props.card.city);
  
  const location = parts.length > 0 ? parts.join(', ') : '未知位置';
  const coordinates = getCoordinatesText();
  
  return `${location} (${coordinates})`;
});

const noLocationText = computed(() => {
  if (!props.card.ip) {
    return 'IP地址获取失败，无法显示位置';
  }
  return '该IP地址没有可用的地理位置信息';
});

const currentMapType = computed(() => mapTypes[currentMapTypeIndex.value].name);

// 方法
const getCoordinatesText = () => {
  const latitude = props.card.latitude;
  const longitude = props.card.longitude;

  if (!latitude || !longitude) return '坐标不可用';
  return `${parseFloat(latitude).toFixed(4)}, ${parseFloat(longitude).toFixed(4)}`;
};

const centerMap = () => {
  if (map && hasValidLocation.value) {
    map.setView(center.value, zoom.value);
  }
};

const toggleMapExpanded = () => {
  isMapExpanded.value = !isMapExpanded.value;
  
  // 如果地图展开且还没有初始化，则初始化地图
  if (isMapExpanded.value && hasValidLocation.value && !map) {
    nextTick(() => {
      initMap();
    });
  }
};

const toggleMapType = () => {
  if (!map) return;

  currentMapTypeIndex.value = (currentMapTypeIndex.value + 1) % mapTypes.length;
  const newMapType = mapTypes[currentMapTypeIndex.value];


  isLoading.value = true;

  // 移除旧的瓦片层
  if (tileLayer) {
    map.removeLayer(tileLayer);
  }

  // 添加新的瓦片层
  tileLayer = window.L.tileLayer(newMapType.url, {
    attribution: newMapType.attribution,
    maxZoom: 18,
    detectRetina: true,
    subdomains: newMapType.subdomains || ['a', 'b', 'c'],
    timeout: 5000,
    errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
  }).addTo(map);

  // 监听新瓦片层的加载事件
  tileLayer.on('load', () => {
    isLoading.value = false;
  });

  tileLayer.on('tileerror', (e) => {
    isLoading.value = false;
  });

  // 设置超时
  setTimeout(() => {
    if (isLoading.value) {

      isLoading.value = false;
    }
  }, 5000);
};

const openInExternalMap = () => {
  if (hasValidLocation.value) {
    const [lat, lng] = center.value;
    const url = `https://www.openstreetmap.org/?mlat=${lat}&mlon=${lng}&zoom=${zoom.value}`;
    window.open(url, '_blank');
  }
};

const destroyMap = () => {
  if (map) {
    map.remove();
    map = null;
    marker = null;
    circle = null;
    tileLayer = null;
  }
};

const createPopupContent = () => {
  const popupContent = document.createElement('div');
  popupContent.className = 'location-popup';

  const title = document.createElement('div');
  title.className = 'popup-title';
  title.textContent = 'IP地理位置信息';
  popupContent.appendChild(title);

  const content = document.createElement('div');
  content.className = 'popup-content';

  // 添加位置信息
  const addLocationItem = (label, value) => {
    if (!value) return;

    const item = document.createElement('div');
    item.className = 'location-item';

    const labelSpan = document.createElement('span');
    labelSpan.className = 'fw-bold';
    labelSpan.textContent = `${label}: `;

    const valueSpan = document.createElement('span');
    valueSpan.textContent = value;

    item.appendChild(labelSpan);
    item.appendChild(valueSpan);
    content.appendChild(item);
  };

  // 添加各种位置信息
  addLocationItem('IP', props.card.ip);
  addLocationItem('国家', props.card.country_name);
  addLocationItem('地区', props.card.region_name);
  addLocationItem('城市', props.card.city);
  addLocationItem('坐标', getCoordinatesText());
  addLocationItem('ISP', props.card.isp);

  popupContent.appendChild(content);
  return popupContent;
};

const initMap = () => {
  if (!hasValidLocation.value) {
    isLoading.value = false;
    return;
  }

  if (!mapContainer.value) {
    isLoading.value = false;
    return;
  }

  if (!window.L) {
    isLoading.value = false;
    return;
  }

  try {
    const [lat, lng] = center.value;


    // 创建地图实例
    map = window.L.map(mapContainer.value, {
      center: [lat, lng],
      zoom: zoom.value,
      zoomControl: true,
      scrollWheelZoom: true,
      doubleClickZoom: true,
      dragging: true
    }).setView([lat, lng], zoom.value);

    // 添加瓦片层
    const currentMapType = mapTypes[currentMapTypeIndex.value];
    tileLayer = window.L.tileLayer(currentMapType.url, {
      attribution: currentMapType.attribution,
      maxZoom: 18,
      detectRetina: true,
      subdomains: currentMapType.subdomains || ['a', 'b', 'c'],
      timeout: 5000, // 5秒超时
      errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==' // 透明1x1像素
    }).addTo(map);

    // 监听瓦片层加载事件
    tileLayer.on('loading', () => {

    });

    tileLayer.on('load', () => {
      isLoading.value = false;
    });

    tileLayer.on('tileerror', (e) => {
      // 如果当前不是高德地图且加载失败，自动切换到高德地图
      if (currentMapTypeIndex.value !== 0) {
        setTimeout(() => {
          currentMapTypeIndex.value = 0; // 切换到高德地图
          toggleMapType();
        }, 1000);
      }
    });

    // 添加标记
    marker = window.L.marker([lat, lng]).addTo(map);

    // 添加圆圈表示精度范围
    circle = window.L.circle([lat, lng], {
      color: '#007bff',
      fillColor: '#007bff',
      fillOpacity: 0.1,
      radius: 5000 // 5km半径
    }).addTo(map);

    // 创建弹出窗口内容
    const popupContent = createPopupContent();
    marker.bindPopup(popupContent);

    // 设置超时，如果5秒后还在加载，强制设置为完成
    setTimeout(() => {
      if (isLoading.value) {
        isLoading.value = false;
      }
    }, 5000);

  } catch (error) {

    isLoading.value = false;
  }
};

// 监听props变化
watch(() => props.card, (newCard) => {


  // 如果地图已经展开且有有效位置，则重新初始化地图
  if (newCard && hasValidLocation.value && isMapExpanded.value) {
    destroyMap();
    setTimeout(() => {
      initMap();
    }, 100); // 延迟确保DOM更新完成
  }
}, { deep: true });

// 生命周期
onMounted(() => {
  // 地图默认折叠，不自动初始化
});

onUnmounted(() => {
  destroyMap();
});
</script>

<style scoped>
.ip-location-map-container {
  margin-bottom: 1rem;
}

.map-title {
  color: #495057;
  font-weight: 600;
  margin: 0;
}

.map-wrapper {
  position: relative;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  overflow: hidden;
  background-color: #f8f9fa;
}

.map-loading {
  min-height: 200px;
}

.map-loading-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 1rem;
  border-radius: 0.375rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.map-container {
  height: 300px;
  width: 100%;
  background-color: #e9ecef;
}

.no-location-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6c757d;
  font-size: 0.9rem;
}

.map-controls {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
}

/* Popup样式 */
.location-popup {
  min-width: 200px;
}

.popup-title {
  font-size: 14px;
  color: #007bff;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 8px;
}

.popup-content {
  font-size: 12px;
}

.location-item {
  margin: 4px 0;
  color: #6c757d;
}

.location-item .fw-bold {
  color: #495057;
}

/* 地图展开按钮样式 */
.map-toggle-btn {
  min-width: 80px;
  border-radius: 4px;
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
  transition: all 0.2s ease-in-out;
}

.map-toggle-btn:hover {
  background-color: rgba(108, 117, 125, 0.1);
  border-color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .map-container {
    height: 250px;
  }

  .map-controls {
    flex-direction: column;
  }

  .map-controls button {
    width: 100%;
    margin-bottom: 0.25rem;
  }
}
</style>
