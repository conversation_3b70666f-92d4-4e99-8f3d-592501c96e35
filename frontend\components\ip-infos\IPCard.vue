<template>
  <div class="card jn-card keyboard-shortcut-card" :class="{
            'dark-mode dark-mode-border': isDarkMode,
            'jn-ip-card1 jn-hover-card': !isMobile && ipGeoSource === 0,
            'jn-ip-card2 jn-hover-card': !isMobile && ipGeoSource !== 0,
        }" :data-card-id="card.id">
    <div class="card-header jn-ip-title jn-link1"
         :class="{ 'dark-mode-title': isDarkMode, 'bg-light': !isDarkMode }" style="font-weight: bold;">
      <span>
        <i class="bi" :class="'bi-' + (index + 1) + '-circle-fill'"></i>
        {{ t('ipInfos.Source') }}: {{ card.source }}
      </span>
      <button @click="$emit('refresh-card', card, index)"
              :class="['btn', isDarkMode ? 'btn-dark dark-mode-refresh' : 'btn-light']"
              :aria-label="'Refresh' + card.source" v-tooltip="t('Tooltips.RefreshIPCard')">
        <i class="bi bi-arrow-clockwise"></i>
      </button>
    </div>
    <div class="p-3 placeholder-glow " :class="{
            'dark-mode-title': isDarkMode,
            'jn-link2-dark': isDarkMode,
            'bg-light': !isDarkMode,
            'jn-link2': !isDarkMode
            }">
      <span class="jn-text col-auto">
        <i class="bi bi-pc-display-horizontal"></i>
      </span>
      <span v-if="card.ip" class="col-10" :class="{ 'jn-ip-font': (isMobile && card.ip.length > 32) }">
        {{ card.ip }}
        <i v-if="isValidIP(card.ip)"
           :class="copiedStatus[card.id] ? 'bi bi-clipboard-check-fill' : 'bi bi-clipboard-plus'"
           @click="copyToClipboard(card.ip, card.id)" role="button"
           v-tooltip="{ title: t('Tooltips.CopyIP'), placement: 'right' }" :aria-label="'Copy' + card.ip"></i>
      </span>
      <span v-else class="placeholder col-10"></span>
    </div>


    <div v-if="(card.asn) || card.ip === '2001:4860:4860::8888'
            " class="card-body" :id="'IPInfo-' + (index + 1)">
      <ul class="list-group list-group-flush" v-if="card.country_name">

        <img v-if="isMapShown" :src="isDarkMode ? card.mapUrl_dark : card.mapUrl"
             class="card-img-top jn-map-image" alt="Map" draggable="false">

        <li class="jn-list-group-item"
            :class="{ 'dark-mode': isDarkMode, 'mobile-list': isMobile && isCardsCollapsed }">
          <span class="jn-text col-auto">
            <i class="bi bi-geo-alt-fill"></i> {{ t('ipInfos.Country') }} : 
          </span>
          <span class="col-10 ">
            {{ card.country_name }}
            <span v-if="card.country_code" :class="'jn-fl fi fi-' + card.country_code.toLowerCase()"></span>
          </span>
        </li>

        <template v-if="!isMobile || !isCardsCollapsed">
          <li class="jn-list-group-item" :class="{ 'dark-mode': isDarkMode }">
            <span class="jn-text col-auto">
              <i class="bi bi-houses"></i>
              {{ t('ipInfos.Region') }} : 
            </span>
            <span class="col-10 ">
              {{ card.region }}
            </span>
          </li>

          <li class="jn-list-group-item" :class="{ 'dark-mode': isDarkMode }">
            <span class="jn-text col-auto">
              <i class="bi bi-sign-turn-right"></i>
              {{ t('ipInfos.City') }} : 
            </span>
            <span class="col-10 ">
              {{ card.city }}
            </span>
          </li>

          <li class="jn-list-group-item" :class="{ 'dark-mode': isDarkMode }">
            <span class="jn-text col-auto">
              <i class="bi bi-ethernet"></i>
              {{ t('ipInfos.ISP') }} : 
            </span>
            <span class="col-10 ">
              {{ card.isp }}
            </span>
          </li>

          <!-- Type information - now always shown if available -->
          <li v-show="ipGeoSource === 0 && card.type && card.type !== t('ipInfos.proxyDetect.type.unknownType')"
              class="jn-list-group-item" :class="{ 'dark-mode': isDarkMode }">
            <span class="jn-text col-auto">
              <i class="bi bi-reception-4"></i>
              {{ t('ipInfos.type') }} : 
            </span>
            <span class="col-10 ">
              {{ card.type }}
              <span v-if="card.proxyOperator !== 'unknown'">
                ( {{ card.proxyOperator }} )
              </span>
            </span>
          </li>

          <!-- Is Proxy information - now always shown if available -->
          <li v-show="ipGeoSource === 0 && card.isProxy && card.isProxy !== t('ipInfos.proxyDetect.unknownProxyType')"
              class="jn-list-group-item" :class="{ 'dark-mode': isDarkMode }">
            <span class="jn-text col-auto">
              <i class="bi bi-shield-fill-check"></i>
              {{ t('ipInfos.isProxy') }} : 
            </span>
            <span class="col-10 ">
              {{ card.isProxy }}
              <span v-if="card.proxyProtocol !== t('ipInfos.proxyDetect.unknownProtocol')">
                ( {{ card.proxyProtocol }} )
              </span>
            </span>
          </li>

          <!-- Quality Score information - now always shown for all cards -->
          <li class="jn-list-group-item" :class="{ 'dark-mode': isDarkMode }">
            <span class="jn-text col-auto">
              <i class="bi bi-speedometer"></i> {{ t('ipInfos.qualityScore') }} : 
            </span>

            <template v-if="isQualityScoreLoading">
              <span class="col-auto">{{ t('ipInfos.loading') }}</span>
            </template>
            <template v-else>
              <span class="col-3 jn-risk-score">
                <span class="progress border" :class="[isDarkMode ? 'border-light bg-dark' : 'border-dark']" role="progressbar" aria-label="Quality Score" :aria-valuenow="currentQualityScore" aria-valuemin="0" aria-valuemax="100">
                  <span class="progress-bar" :class="[isDarkMode ? 'bg-light' : 'bg-dark']" :style="{ width: currentQualityScore + '%' }"></span>
                </span>
              </span>
              <span class="ps-2">
                {{ currentQualityScore }}%
                <i class="bi bi-info-circle-fill ms-1"
                   role="button"
                   tabindex="0"
                   data-bs-toggle="tooltip"
                   data-bs-placement="top"
                   :data-bs-title="qualityScoreExplanation || t('Tooltips.qualityScoreExplainDetail')"
                   :aria-label="t('Tooltips.qualityScoreExplainDetail')"
                   @click="openQualityScoreGuide"
                   style="cursor: pointer;"></i>
              </span>
            </template>
          </li>

          <li class="jn-list-group-item border-0" :class="{ 'dark-mode': isDarkMode }">
            <span class="jn-text col-auto">
              <i class="bi bi-buildings"></i>
              {{ t('ipInfos.ASN') }} : 
            </span>
            <span v-if="card.asnlink" class="col-9">
              {{ card.asn }}
              <i v-if="configs.cloudFlare" class="bi bi-info-circle" @click="getASNInfo(card.asn)"
                 data-bs-toggle="collapse" :data-bs-target="'#' + 'collapseASNInfo-' + index"
                 aria-expanded="false" :aria-controls="'collapseASNInfo-' + index" role="button"
                 :aria-label="'Display AS Info of' + card.asn"
                 v-tooltip="{ title: t('Tooltips.ShowASNInfo'), placement: 'right' }">
              </i>
            </span>
          </li>
        </template>

        <ASNInfo :index="index" :isDarkMode="isDarkMode" :asn="card.asn" :asnInfos="asnInfos" />

        <!-- IP地理位置地图 -->
        <IPLocationMap
          :card="card"
          :isDarkMode="isDarkMode"
        />
      </ul>
    </div>

    <div v-else-if="(card.ip === t('ipInfos.IPv4Error')) || (card.ip === t('ipInfos.IPv6Error'))"
         class="card-body jn-ip-error">
      <div class="ip-error-img-wrapper">
        <img src="https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/image/ip.gif"
             alt="IP Error"
             class="ip-error-img"
             draggable="false" />
      </div>
    </div>

    <div v-else class="card-body">
      <ul class="list-group list-group-flush placeholder-glow">
        <li v-for="(colSize, idx) in placeholderSizes" :key="idx" class="list-group-item jn-list-group-item"
            :class="{ 'dark-mode': isDarkMode }">
          <span :class="`placeholder col-${colSize}`"></span>
        </li>
      </ul>
    </div>

  </div>
</template>

<script setup>
  import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { isValidIP } from '@/utils/valid-ip.js';
  import ASNInfo from './ASNInfo.vue';
  import IPLocationMap from './IPLocationMap.vue';
  import { trackEvent } from '@/utils/use-analytics';
  import { Tooltip } from 'bootstrap'; // Import Bootstrap Tooltip

  const { t } = useI18n();

  const placeholderSizes = [12, 8, 6, 8, 4];

  const props = defineProps({
    card: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    isDarkMode: {
      type: Boolean,
      required: true
    },
    isMobile: {
      type: Boolean,
      required: true
    },
    ipGeoSource: {
      type: Number,
      required: true
    },
    isMapShown: {
      type: Boolean,
      required: true
    },
    isCardsCollapsed: {
      type: Boolean,
      required: true
    },
    copiedStatus: {
      type: Object,
      required: true
    },
    configs: {
      type: Object,
      required: true
    },
    asnInfos: {
      type: Object,
      required: true
    }
  });

  const emit = defineEmits(['refresh-card', 'get-asn-info']);

  const copyToClipboard = (ip, id) => {
    navigator.clipboard.writeText(ip).then(() => {
      props.copiedStatus[id] = true;
      setTimeout(() => {
        props.copiedStatus[id] = false;
      }, 5000);
    }).catch(err => {
      console.error('Copy error', err);
    });
  };

  // 打开IP质量评分系统详解PDF
  const openQualityScoreGuide = () => {
    trackEvent('IPCheck', 'QualityScoreGuideClick', 'Open Quality Score Guide');
    window.open('https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/file/markdown.pdf', '_blank', 'noopener,noreferrer');
  };

  const getASNInfo = async (asn) => {
    trackEvent('IPCheck', 'ASNInfoClick', 'Show ASN Info');
    try {
      if (props.asnInfos[asn]) {
        return;
      }
      asn = asn.replace('AS', '');
      const response = await fetch(`/api/cfradar?asn=${asn}`);
      const data = await response.json();
      props.asnInfos['AS' + asn] = data;
    } catch (error) {
      console.error("Error fetching ASN info:", error);
    }
  };

  // --- Quality Score Logic ---
  const currentQualityScore = ref(0);
  const isQualityScoreLoading = ref(true);
  const qualityScoreExplanation = ref('');
  let qualityScoreTimer = null;

  const QUALITY_SCORE_TIMEOUT = 3000; // 3 seconds

  const processQualityScore = () => {
    clearTimeout(qualityScoreTimer);
    isQualityScoreLoading.value = true;
    currentQualityScore.value = 0;
    qualityScoreExplanation.value = '';

    const rawScore = props.card.qualityScore;

    // If score is already a valid number, display it immediately
    if (typeof rawScore === 'number' && !isNaN(rawScore)) {
      currentQualityScore.value = Math.min(Math.max(Math.round(rawScore), 0), 100);
      isQualityScoreLoading.value = false;
      return;
    }

    // Otherwise, start timer for fallback calculation
    qualityScoreTimer = setTimeout(() => {
      const scoreAtTimeout = props.card.qualityScore;
      if (typeof scoreAtTimeout === 'number' && !isNaN(scoreAtTimeout)) {
        currentQualityScore.value = Math.min(Math.max(Math.round(scoreAtTimeout), 0), 100);
      } else {
        // Calculate real quality score based on IP data
        calculateRealQualityScore();
      }
      isQualityScoreLoading.value = false;
    }, QUALITY_SCORE_TIMEOUT);
  };

  const calculateRealQualityScore = async () => {
    try {
      // 如果没有IP地址，显示默认分数
      if (!props.card.ip || props.card.ip === t('ipInfos.IPv4Error') || props.card.ip === t('ipInfos.IPv6Error')) {
        currentQualityScore.value = 0;
        qualityScoreExplanation.value = 'IP地址获取失败，无法评估质量';
        return;
      }

      // 动态导入质量分计算模块
      const { calculateIPQualityScore } = await import('@/utils/ip-quality-score.js');

      const result = calculateIPQualityScore(props.card, props.card.ip);
      currentQualityScore.value = result.score;
      qualityScoreExplanation.value = result.explanation.details;
      // 初始化tooltip
      initializeTooltips();
    } catch (error) {
      console.error('Error calculating quality score:', error);
      // 如果计算失败，使用基于IP信息的简单评估
      currentQualityScore.value = getSimpleQualityScore();
      qualityScoreExplanation.value = '基于IP基础信息的简化评估';
      // 初始化tooltip
      initializeTooltips();
    }
  };

  const getSimpleQualityScore = () => {
    let score = 75; // 基础分数

    // 基于现有数据的简单评估
    if (props.card.country_name && props.card.city) score += 10;
    if (props.card.isp && !props.card.isp.toLowerCase().includes('hosting')) score += 10;
    if (props.card.asn) score += 5;

    return Math.min(100, Math.max(0, score));
  };

  watch(() => props.card.id,
    () => {
      processQualityScore();
    },
    { immediate: true }
  );

  watch(() => props.card.qualityScore, (newScore) => {
    if (typeof newScore === 'number' && !isNaN(newScore) && isQualityScoreLoading.value) {
      clearTimeout(qualityScoreTimer);
      currentQualityScore.value = Math.min(Math.max(Math.round(newScore), 0), 100);
      isQualityScoreLoading.value = false;
      // 初始化tooltip
      initializeTooltips();
    } else if (isQualityScoreLoading.value === false && (typeof newScore !== 'number' || isNaN(newScore))) {
      // If the score changes to an invalid state after already loaded, re-process
      processQualityScore();
    }
  });

  // Store tooltip instances to dispose them correctly
  let activeTooltips = [];

  const initializeTooltips = () => {
    nextTick(() => {
      // 查找当前组件内的tooltip元素
      const tooltipTriggerList = Array.from(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
      activeTooltips = tooltipTriggerList.map(tooltipTriggerEl => {
        // Dispose existing instance if any, then create new
        const existingTooltip = Tooltip.getInstance(tooltipTriggerEl);
        if (existingTooltip) {
          existingTooltip.dispose();
        }
        // 创建新的tooltip实例，配置为在移动设备上也显示
        return new Tooltip(tooltipTriggerEl, {
          trigger: 'hover focus click',
          placement: 'top'
        });
      });
      console.log('Tooltips initialized:', activeTooltips.length);
    });
  };

  onMounted(() => {
    // 组件挂载后初始化tooltip
    initializeTooltips();
  });

  onBeforeUnmount(() => {
    clearTimeout(qualityScoreTimer);
    activeTooltips.forEach(tooltip => {
      if (tooltip) {
        tooltip.dispose();
      }
    });
    activeTooltips = [];
  });

</script>

<!-- Global styles: NOT scoped -->
<style>
  img {
    -webkit-user-drag: none; /* Safari, Chrome */
    user-drag: none; /* Standard */
    -moz-user-select: none; /* Firefox - prevent selection which can lead to drag */
    -webkit-user-select: none; /* Safari, Chrome - prevent selection */
    -ms-user-select: none; /* IE/Edge - prevent selection */
    user-select: none; /* Standard - prevent selection */
  }
</style>

<style scoped>
  .jn-link1 {
    position: relative;
  }

  .jn-link2::before {
    content: '';
    position: absolute;
    top: 34px;
    left: 24px;
    transform: translateX(-50%);
    height: 40px;
    width: 2px;
    border-left: 2px dashed #212529;
    z-index: 1;
  }

  .jn-link2-dark::before {
    content: '';
    position: absolute;
    top: 34px;
    left: 24px;
    transform: translateX(-50%);
    height: 40px;
    width: 2px;
    border-left: 2px dashed #e3e3e3;
    z-index: 1;
  }

  .jn-ip-error {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-direction: column;
    width: 100%;
    height: 100%;
  }

  .ip-error-img-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ip-error-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
</style>