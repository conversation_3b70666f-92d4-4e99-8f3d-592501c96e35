// 合并的外部服务API
// import { refererMiddleware } from '../middleware/referer-middleware.js';

// 简化的referer检查中间件
const refererMiddleware = (req, res, next) => {
  // 在Vercel环境中简化referer检查
  next();
};

// Cloudflare Radar API
const cfRadarHandler = async (req, res) => {
  try {
    const { endpoint, ...params } = req.query;
    
    if (!endpoint) {
      return res.status(400).json({ error: 'Endpoint parameter is required' });
    }

    const baseUrl = 'https://api.cloudflare.com/client/v4/radar';
    const url = new URL(`${baseUrl}/${endpoint}`);
    
    // 添加查询参数
    Object.keys(params).forEach(key => {
      if (params[key]) {
        url.searchParams.append(key, params[key]);
      }
    });

    const response = await fetch(url.toString(), {
      headers: {
        'Authorization': `Bearer ${process.env.CLOUDFLARE_API_TOKEN || ''}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error fetching from Cloudflare Radar:', error);
    res.status(500).json({ error: 'Failed to fetch data from Cloudflare Radar' });
  }
};

// Google Maps API
const googleMapHandler = async (req, res) => {
  try {
    const { service, ...params } = req.query;
    
    if (!service) {
      return res.status(400).json({ error: 'Service parameter is required' });
    }

    const apiKey = process.env.GOOGLE_MAPS_API_KEY;
    if (!apiKey) {
      return res.status(500).json({ error: 'Google Maps API key not configured' });
    }

    let baseUrl;
    switch (service) {
      case 'geocoding':
        baseUrl = 'https://maps.googleapis.com/maps/api/geocode/json';
        break;
      case 'places':
        baseUrl = 'https://maps.googleapis.com/maps/api/place/textsearch/json';
        break;
      case 'directions':
        baseUrl = 'https://maps.googleapis.com/maps/api/directions/json';
        break;
      default:
        return res.status(400).json({ error: 'Invalid service parameter' });
    }

    const url = new URL(baseUrl);
    url.searchParams.append('key', apiKey);
    
    // 添加其他查询参数
    Object.keys(params).forEach(key => {
      if (params[key]) {
        url.searchParams.append(key, params[key]);
      }
    });

    const response = await fetch(url.toString());
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error fetching from Google Maps:', error);
    res.status(500).json({ error: 'Failed to fetch data from Google Maps' });
  }
};

// 隐身测试API - 使用外部服务
const invisibilityTestHandler = async (req, res) => {
  try {
    const { id, realip } = req.query;

    // 使用外部API进行隐身测试
    try {
      let apiUrl = `https://myip.wobys.dpdns.org/api/invisibility?id=${encodeURIComponent(id || 'test')}`;
      if (realip) {
        apiUrl += `&realip=${encodeURIComponent(realip)}`;
      }

      const response = await fetch(apiUrl, {
        timeout: 30000 // 30秒超时
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      res.json(data);
    } catch (error) {
      console.error('External invisibility API error:', error);

      // 返回模拟数据作为备用
      const clientIP = req.headers['cf-connecting-ip'] ||
                       req.headers['x-real-ip'] ||
                       req.headers['x-forwarded-for']?.split(',')[0].trim() ||
                       '127.0.0.1';

      res.json({
        ip: realip || clientIP,
        score: {
          proxy: Math.floor(Math.random() * 30), // 0-30% 随机分数
          vpn: Math.floor(Math.random() * 30)
        },
        isProxy: false,
        message: 'Using fallback data - external service unavailable',
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('Error in invisibility test:', error);
    res.status(500).json({ error: 'Invisibility test failed', message: error.message });
  }
};

// 主处理函数
export default async function handler(req, res) {
  refererMiddleware(req, res, async () => {
    const { service } = req.query;
    
    switch (service) {
      case 'cf-radar':
        return cfRadarHandler(req, res);
      case 'google-map':
        return googleMapHandler(req, res);
      case 'invisibility':
        return invisibilityTestHandler(req, res);
      default:
        res.status(400).json({ error: 'Invalid service parameter' });
    }
  });
}
