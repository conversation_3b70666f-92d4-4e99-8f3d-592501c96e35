<template>
  <span
    class="jn-speedtest-number animated-number"
    :class="{
      'download-active': active && type === 'download',
      'upload-active': active && type === 'upload',
      'latency-active': active && type === 'latency',
      'jitter-active': active && type === 'jitter',
      'updating': isUpdating
    }"
  >
    {{ displayValue }}
  </span>
</template>

<script setup>
import { ref, watch, computed } from 'vue';

const props = defineProps({
  value: {
    type: [Number, String],
    default: '-'
  },
  active: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'default'
  }
});

const displayValue = ref(props.value);
const isUpdating = ref(false);
let animationFrame = null;

// 格式化显示值
const formatValue = (value) => {
  if (value === "-" || value === 0) return "-";
  if (typeof value === 'number') {
    return value.toFixed(2);
  }
  return value;
};

// 数值动画函数
const animateToValue = (startValue, endValue, duration = 200) => {
  if (Math.abs(parseFloat(startValue) - parseFloat(endValue)) < 0.01) return;

  const startTime = performance.now();
  const startNum = parseFloat(startValue) || 0;
  const endNum = parseFloat(endValue) || 0;
  const diff = endNum - startNum;

  const animate = (currentTime) => {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // 使用更平滑的缓动函数
    const easeOutCubic = 1 - Math.pow(1 - progress, 3);
    const currentValue = startNum + (diff * easeOutCubic);

    displayValue.value = formatValue(currentValue);

    if (progress < 1) {
      animationFrame = requestAnimationFrame(animate);
    } else {
      displayValue.value = formatValue(endValue);
      isUpdating.value = false;
    }
  };

  if (animationFrame) {
    cancelAnimationFrame(animationFrame);
  }

  isUpdating.value = true;
  animationFrame = requestAnimationFrame(animate);
};

// 监听值变化
watch(() => props.value, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    const newNum = parseFloat(newValue);
    const oldNum = parseFloat(oldValue);

    // 从"-"到数字的变化，直接显示
    if (oldValue === "-" && !isNaN(newNum) && newNum > 0) {
      displayValue.value = formatValue(newValue);
      isUpdating.value = false;
    }
    // 只有在有效数值之间变化且变化幅度足够大时才使用动画
    else if (!isNaN(newNum) && !isNaN(oldNum) && newNum > 0 && oldNum > 0 && Math.abs(newNum - oldNum) >= 0.1) {
      animateToValue(oldValue, newValue);
    } else {
      // 直接更新显示值
      displayValue.value = formatValue(newValue);
      isUpdating.value = false;
    }
  }
}, { immediate: true });

// 清理动画
const cleanup = () => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame);
    animationFrame = null;
  }
};

// 组件卸载时清理
import { onUnmounted } from 'vue';
onUnmounted(cleanup);
</script>

<style scoped>
.animated-number {
  transition: all 0.2s ease-in-out;
  display: inline-block;
  position: relative;
}

.animated-number.updating {
  transform: scale(1.03);
}

.animated-number.download-active {
  color: #0dcaf0;
  text-shadow: 0 0 15px rgba(13, 202, 240, 0.4);
  animation: pulse-download 2s ease-in-out infinite;
}

.animated-number.upload-active {
  color: #20c997;
  text-shadow: 0 0 15px rgba(32, 201, 151, 0.4);
  animation: pulse-upload 2s ease-in-out infinite;
}

.animated-number.latency-active,
.animated-number.jitter-active {
  color: #ffc107;
  text-shadow: 0 0 15px rgba(255, 193, 7, 0.4);
  animation: pulse-latency 2s ease-in-out infinite;
}

@keyframes pulse-download {
  0%, 100% {
    text-shadow: 0 0 15px rgba(13, 202, 240, 0.4);
  }
  50% {
    text-shadow: 0 0 25px rgba(13, 202, 240, 0.6);
  }
}

@keyframes pulse-upload {
  0%, 100% {
    text-shadow: 0 0 15px rgba(32, 201, 151, 0.4);
  }
  50% {
    text-shadow: 0 0 25px rgba(32, 201, 151, 0.6);
  }
}

@keyframes pulse-latency {
  0%, 100% {
    text-shadow: 0 0 15px rgba(255, 193, 7, 0.4);
  }
  50% {
    text-shadow: 0 0 25px rgba(255, 193, 7, 0.6);
  }
}
</style>
