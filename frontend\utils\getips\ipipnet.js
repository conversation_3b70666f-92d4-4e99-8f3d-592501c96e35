import { getIPFromUpai } from "./upai";
import { isValidIP } from '@/utils/valid-ip.js';

// 从 IPIP.net 获取 IP 地址
const getIPFromIPIP = async () => {
    try {
        const response = await fetch("https://myip.ipip.net/json");
        const data = await response.json();
        const ip = data.data.ip;
      const source = "𝓌𝑜𝒷.IPAPI";
        if (isValidIP(ip)) {
            return {
                ip: ip,
                source: source
            };
        } else {
            console.error("Invalid IP from 𝓌𝑜𝒷.IPAPI:", ip);
        }
    } catch (error) {
        console.error("Error fetching IP from 𝓌𝑜𝒷.IPAPI:", error);
    }
    // 故障时尝试从 AliCDN 获取 IP 地址
    const { ip, source } = await getIPFromUpai();
    return {
        ip: ip,
        source: source
    };
};

export { getIPFromIPIP };