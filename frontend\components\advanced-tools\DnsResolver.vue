<template>
  <!-- DNS Resolver -->
  <div class="dns-resolver-section my-4">
    <div class="text-secondary">
      <p>{{ t('dnsresolver.Note') }}</p>
    </div>
    <div class="row">
      <div class="col-12 mb-3">
        <div class="card jn-card" :class="{ 'dark-mode dark-mode-border': isDarkMode }">
          <div class="card-body">
            <div class="col-12 col-md-auto">
              <label for="queryURL" class="col-form-label">{{ t('dnsresolver.Note2') }}</label>
            </div>

            <div class="input-group mb-2 mt-2 ">
              <input type="text" class="form-control" :class="{ 'dark-mode': isDarkMode }"
                     :disabled="dnsCheckStatus === 'running'" :placeholder="t('dnsresolver.Placeholder')"
                     v-model="queryURL" @keyup.enter="onSubmit" name="queryURL" id="queryURL" data-1p-ignore>

              <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split"
                      data-bs-toggle="dropdown" aria-expanded="false"
                      :disabled="dnsCheckStatus === 'running' || !queryURL">
                {{ queryType }} {{ t('dnsresolver.Record') }}
                <span class="visually-hidden">Choose Type</span>
              </button>
              <ul class="dropdown-menu dropdown-menu-end">
                <li v-for="typeInList in ['A', 'AAAA', 'CNAME', 'MX', 'NS', 'TXT']" :key="typeInList"
                    @click="changeType(typeInList)">
                  <span class="dropdown-item">{{ typeInList }}</span>
                </li>
              </ul>
              <button class="btn btn-primary" @click="onSubmit"
                      :disabled="dnsCheckStatus === 'running' || !queryURL">
                <span v-if="dnsCheckStatus === 'idle'">
                  {{
                                    t('dnsresolver.Run')
                  }}
                </span>
                <span v-if="dnsCheckStatus === 'running'" class="spinner-grow spinner-grow-sm"
                      aria-hidden="true"></span>
              </button>
            </div>
            <div class="jn-placeholder">
              <p v-if="errorMsg" class="text-danger">{{ errorMsg }}</p>
            </div>

            <!-- 添加记录类型说明和示例 -->
            <div class="alert alert-info mb-3" style="font-size: 0.85em;" :class="{ 'alert-dark': isDarkMode }">
              <div class="row">
                <div class="col-md-6">
                  <strong>📝 记录类型说明：</strong><br>
                  • <strong>A</strong>: IPv4地址记录<br>
                  • <strong>AAAA</strong>: IPv6地址记录<br>
                  • <strong>CNAME</strong>: 别名记录（通常用于www子域名）
                </div>
                <div class="col-md-6">
                  • <strong>MX</strong>: 邮件交换记录<br>
                  • <strong>TXT</strong>: 文本记录（验证、SPF等）<br>
                  • <strong>NS</strong>: 域名服务器记录
                </div>
              </div>
              <hr class="my-2">
              <strong>💡 测试建议：</strong>
              <span class="ms-2">
                <button class="btn btn-sm btn-outline-primary me-1" @click="setExample('google.com', 'TXT')">google.com TXT</button>
                <button class="btn btn-sm btn-outline-primary me-1" @click="setExample('google.com', 'MX')">google.com MX</button>
                <button class="btn btn-sm btn-outline-primary me-1" @click="setExample('www.github.com', 'CNAME')">www.github.com CNAME</button>
                <button class="btn btn-sm btn-outline-primary" @click="setExample('cloudflare.com', 'NS')">cloudflare.com NS</button>
              </span>
            </div>

            <!-- Results Table -->
            <div v-if="combinedResults && combinedResults.length">
              <div class="table-responsive text-nowrap">
                <table class="table table-hover" :class="{ 'table-dark': isDarkMode }">
                  <thead>
                    <tr>
                      <th scope="col">{{ t('dnsresolver.Provider') }}</th>
                      <th scope="col">{{ t('dnsresolver.Result') }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(result, index) in combinedResults" :key="index">
                      <td>{{ result.provider }}</td>
                      <td :class="{'text-danger': result.isError, 'opacity-50': result.isNA && !result.isError }">
                        <span v-if="result.address === 'ENODATA'" class="text-muted">
                          <i class="fas fa-info-circle me-1"></i>
                          无此记录类型
                        </span>
                        <span v-else>{{ result.address }}</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- 添加结果说明 -->
              <div class="mt-3">
                <small class="text-muted">
                  <i class="fas fa-lightbulb me-1"></i>
                  <strong>说明：</strong>
                  "无此记录类型" 表示该域名没有配置此类型的DNS记录，这是正常现象。
                  不同的域名会配置不同类型的记录，例如：根域名通常没有CNAME记录，子域名可能没有MX记录等。
                </small>
              </div>
            </div>
            <div v-else-if="dnsCheckStatus === 'idle' && queryURL && !errorMsg && initialCheckDone">
              <p class="text-secondary">{{ t('dnsresolver.noResultsOrFormatError') }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { useMainStore } from '@/store';
  import { useI18n } from 'vue-i18n';
  import { trackEvent } from '@/utils/use-analytics';

  const { t } = useI18n();

  const store = useMainStore();
  const isDarkMode = computed(() => store.isDarkMode);

  const queryURL = ref('');
  const queryType = ref('A');
  const dnsCheckStatus = ref('idle');
  const errorMsg = ref('');
  const combinedResults = ref([]);
  const initialCheckDone = ref(false);

  const validateInput = (input) => {
    if (!input || typeof input !== 'string' || input.trim() === '') {
      errorMsg.value = t('dnsresolver.inputRequired');
      return null;
    }
    let normalizedInput = input.trim();

    // 移除非法字符 - 这只是一个非常基础的清理，不完美
    // 更健壮的做法是严格验证域名格式
    // normalizedInput = normalizedInput.replace(/[^a-zA-Z0-9.-]/g, ''); // 移除美元符等

    // 尝试提取主机名
    try {
      // 如果包含协议，直接用URL解析
      if (normalizedInput.includes("://")) {
        const url = new URL(normalizedInput);
        if (url.hostname) {
          // 进一步验证主机名是否合法 (不含非法字符)
          if (/^[a-zA-Z0-9.-]+$/.test(url.hostname) && !url.hostname.startsWith('-') && !url.hostname.endsWith('-')) {
            return url.hostname;
          }
        }
      } else if (normalizedInput.includes('.')) {
        // 如果不包含协议但包含点，认为是裸域名
        const domainRegex = /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,61}$/i; // 顶级域名长度调整
        if (domainRegex.test(normalizedInput) && !normalizedInput.startsWith('-') && !normalizedInput.endsWith('-') && !normalizedInput.includes('..')) {
          return normalizedInput;
        }
      }
    } catch (e) {
      console.warn("URL parsing failed for input:", input, e);
    }

    errorMsg.value = t('dnsresolver.invalidURL');
    return null;
  };


  const changeType = (newType) => {
    queryType.value = newType;
  };

  // 设置示例域名和记录类型
  const setExample = (hostname, type) => {
    queryURL.value = hostname;
    queryType.value = type;
    // 清除之前的错误信息和结果
    errorMsg.value = '';
    combinedResults.value = [];
    initialCheckDone.value = false;
  };

  const onSubmit = () => {
    trackEvent('Section', 'StartClick', 'DNSResolver');
    errorMsg.value = '';
    combinedResults.value = [];
    initialCheckDone.value = false;

    const hostname = validateInput(queryURL.value);
    const currentQueryType = queryType.value;

    if (hostname) {
      getDNSResults(hostname, currentQueryType);
    }
  };

  const getDNSResults = async (hostname, type) => {
    dnsCheckStatus.value = 'running';
    try {
      const apiUrl = `/api/tools?tool=dns&hostname=${encodeURIComponent(hostname)}&type=${encodeURIComponent(type)}`;

      console.log("请求 API URL:", apiUrl);
      const response = await fetch(apiUrl);
      console.log("API 响应状态:", response.status, response.statusText);

      if (!response.ok) {
        let errorDetails = `Network response was not ok (${response.status} ${response.statusText})`;
        let responseText = '';
        try {
          responseText = await response.text();
          console.error("API 错误响应体:", responseText);
          try {
            const jsonData = JSON.parse(responseText);
            if (jsonData && (jsonData.error || jsonData.message)) {
              errorDetails += ` - ${jsonData.error || jsonData.message}`;
            } else {
              errorDetails += ` - ${responseText.substring(0, 100)}`;
            }
          } catch (parseError) {
            errorDetails += ` - ${responseText.substring(0, 100)}`;
          }
        } catch (e) { console.error("读取API错误响应体失败:", e); }
        throw new Error(errorDetails);
      }
      const data = await response.json();
      console.log("从 API 获取的原始数据 (data):", JSON.stringify(data, null, 2));
      processResults(data);
    } catch (error) {
      console.error('获取 DNS 结果时出错:', error);
      errorMsg.value = t('dnsresolver.fetchError') + (error.message ? `: ${error.message}` : '');
    } finally {
      dnsCheckStatus.value = 'idle';
      initialCheckDone.value = true;
    }
  };

  const processResults = (data) => {
    const tempResults = [];

    if (data && data.results && typeof data.results === 'object') {
      for (const providerName in data.results) {
        if (Object.prototype.hasOwnProperty.call(data.results, providerName)) {
          const providerEntry = data.results[providerName];
          let address = 'N/A';
          let isError = false;
          let isNA = true; // Assume N/A unless a valid address is found or it's a known error

          console.log(`处理提供商: ${providerName}`, providerEntry);

          if (providerEntry && typeof providerEntry === 'object') {
            // 1. 优先处理 API 返回的明确错误
            if (providerEntry.error) {
              address = providerEntry.error;
              isError = true;
              isNA = false; // It's an error, not just N/A
            }
            // 2. 处理基于 status 的错误 (如 NXDOMAIN)
            else if (providerEntry.status === 3) { // 3 通常表示 NXDOMAIN
              address = "NXDOMAIN (域名不存在)";
              isError = true; // NXDOMAIN is an error from DNS perspective
              isNA = false;
            }
            // 你可以根据 API 文档添加更多 status code 的处理
            // else if (providerEntry.status === SOME_OTHER_ERROR_CODE) { ... }

            // 3. 如果没有错误，尝试提取成功的地址 (主要基于 'addresses' 数组)
            else if (providerEntry.addresses && Array.isArray(providerEntry.addresses)) {
              if (providerEntry.addresses.length > 0) {
                address = providerEntry.addresses.join(', ');
                isNA = false; // Found addresses
              } else {
                // addresses 数组为空，但没有明确错误，可能是 NODATA
                // (A 记录存在但没有 IPv4 地址，或者 AAAA 记录存在但没有 IPv6 地址)
                address = "NODATA (无记录)"; // 或者 t('dnsresolver.nodata')
                isNA = true; // Still N/A in terms of usable data for this type
              }
            }
            // 4. 为其他记录类型或不同的成功响应结构添加后备逻辑
            //    这些需要根据你对 API 返回特定类型记录（MX, TXT, CNAME等）的了解来编写
            else {
              // 例如，CNAME记录可能在 'target' 或 'cname' 键下
              if (queryType.value === 'CNAME' && typeof providerEntry.target === 'string') {
                address = providerEntry.target;
                isNA = false;
              } else if (queryType.value === 'CNAME' && typeof providerEntry.cname === 'string') {
                address = providerEntry.cname;
                isNA = false;
              }
              // TXT 记录可能是字符串数组或单个长字符串
              else if (queryType.value === 'TXT') {
                if (Array.isArray(providerEntry.text) && providerEntry.text.length > 0) { // 假设在 'text' 键下
                  address = providerEntry.text.map(txt => `"${txt}"`).join(' '); // TXT 记录通常用引号括起来
                  isNA = false;
                } else if (typeof providerEntry.text === 'string' && providerEntry.text.trim() !== '') {
                  address = `"${providerEntry.text}"`;
                  isNA = false;
                } else {
                  address = 'N/A (TXT 结构未知)';
                  isNA = true;
                }
              }
              // MX 记录
              else if (queryType.value === 'MX' && Array.isArray(providerEntry.exchanges) && providerEntry.exchanges.length > 0) {
                address = providerEntry.exchanges
                  .map(ex => `${ex.priority || '?'} ${ex.exchange || '?'}`) // 假设 MX 记录在 exchanges 数组中，每个对象有 priority 和 exchange
                  .join('; ');
                isNA = false;
              }
              // NS 记录
              else if (queryType.value === 'NS' && Array.isArray(providerEntry.nameservers) && providerEntry.nameservers.length > 0) {
                address = providerEntry.nameservers.join(', '); // 假设 NS 记录在 nameservers 数组中
                isNA = false;
              }
              // 如果以上都不匹配，则是未知结构
              else {
                address = 'N/A (结构无法识别)';
                isNA = true;
                console.warn(`提供商 '${providerName}' (类型: ${queryType.value}) 的数据结构无法直接解析为地址或已知错误/结构:`, providerEntry);
              }
            }
          } else if (typeof providerEntry === 'string') { // 后备：如果整个 providerEntry 是字符串
            address = providerEntry;
            isNA = (address.trim() === '');
          } else {
            address = 'N/A (无效条目)';
            isNA = true;
            console.warn(`提供商 '${providerName}' 的条目格式无效:`, providerEntry);
          }

          // 使用本地化的DNS服务商名称
          const localizedProviderName = t(`dnsresolver.providers.${providerName}`) || providerName;
          tempResults.push({ provider: localizedProviderName, address: address, isError: isError, isNA: isNA });
        }
      }
    } else {
      console.warn("API 响应数据结构不符合预期，缺少 'results' 对象或 'results' 不是对象:", data);
      if (!errorMsg.value && initialCheckDone.value) {
        // errorMsg.value = t('dnsresolver.apiResponseFormatError');
      }
    }

    combinedResults.value = tempResults;
    console.log("处理后的 combinedResults:", JSON.stringify(combinedResults.value, null, 2));
  };

  // i18n keys (ensure these exist in your translation files)
  // t('dnsresolver.Note')
  // t('dnsresolver.Note2')
  // t('dnsresolver.Placeholder')
  // t('dnsresolver.Record')
  // t('dnsresolver.Run')
  // t('dnsresolver.Provider')
  // t('dnsresolver.Result')
  // t('dnsresolver.inputRequired')
  // t('dnsresolver.invalidURL')
  // t('dnsresolver.fetchError')
  // t('dnsresolver.apiResponseFormatError')
  // t('dnsresolver.noResultsOrFormatError') // Updated from noResultsFound for clarity
  // t('dnsresolver.nodata') // For NODATA responses
</script>

<style scoped>
  .jn-placeholder {
    min-height: 1.5rem;
    display: flex;
    align-items: center;
  }

  .opacity-50 {
    opacity: 0.5;
  }

  .text-danger { /* Bootstrap class, but good to be explicit if needed */
    color: var(--bs-danger); /* Or your specific danger color */
  }
</style>