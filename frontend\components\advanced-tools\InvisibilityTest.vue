<template>
    <!-- InvisibilityTest Resolver -->
    <div class="invisibilitytest-section my-4">
        <div class="text-secondary mb-3">
            <div v-html="t('invisibilitytest.Note')" class="feature-description"></div>
        </div>
        <div class="row">
            <div class="col-12 mb-3">
                <div class="card jn-card" :class="{ 'dark-mode dark-mode-border': isDarkMode }">
                    <div class="card-body">
                        <div class="col-12 col-md-auto mt-2 mb-3">
                            <div v-html="t('invisibilitytest.Note2')" class="usage-instructions"></div>
                            <div class="alert alert-info mt-2" role="alert">
                                <i class="bi bi-info-circle"></i>
                                <strong>VPN检测原理：</strong>通过WebRTC技术检测您的真实IP地址，然后与当前显示的IP地址进行比较。如果两者不同，说明您可能在使用VPN或代理服务。
                            </div>
                        </div>

                        <div class="input-group mb-2 mt-3">

                            <div class="input-group-text">
                                <input class="form-check-input mt-0" type="checkbox" value=""
                                    aria-label="Checkbox for Collecting datas" name="collectingDatas"
                                    id="collectingDatas" v-model="isAgreed">
                                <label for="collectingDatas">&nbsp;{{ t('invisibilitytest.agreement') }}</label>
                            </div>

                            <button class="btn btn-primary" @click="onSubmit"
                                :disabled="checkingStatus === 'running' || !isAgreed">
                                <span v-if="checkingStatus === 'idle'">{{
                                    t('invisibilitytest.Run') }}</span>
                                <span v-if="checkingStatus === 'running'" class="spinner-grow spinner-grow-sm"
                                    aria-hidden="true"></span>
                            </button>

                        </div>

                        <div class="jn-placeholder">
                            <p v-if="errorMsg" class="text-danger">{{ errorMsg }}</p>
                        </div>

                        <!-- Results Table -->
                        <Transition name="jn-it-slide-fade">
                            <div class="alert alert-success" role="alert"
                                v-if="Object.keys(testResults).length > 0">

                                <p>{{ t('invisibilitytest.yourIP') }}: <strong>{{ testResults.ip }}</strong>.</p>

                                <!-- 显示真实IP对比 -->
                                <p v-if="testResults.realIP && testResults.realIP !== testResults.ip">
                                    <i class="bi bi-exclamation-triangle-fill text-warning"></i>
                                    真实IP: <strong>{{ testResults.realIP }}</strong>
                                    <small class="text-muted">(与当前IP不同)</small>
                                </p>

                                <p><i class="bi bi-lock-fill"></i> {{ t('invisibilitytest.proxyScore') }}:
                                    {{ testResults.score.proxy }}%.
                                </p>
                                <p><i class="bi bi-shield-lock-fill"></i> {{ t('invisibilitytest.VPNScore') }}:
                                    {{ testResults.score.vpn }}%.
                                </p>

                                <!-- 显示检测方法 -->
                                <div v-if="testResults.detectionMethods && testResults.detectionMethods.length > 0" class="mt-3">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i> 检测依据:
                                        <span v-for="(method, index) in testResults.detectionMethods" :key="index">
                                            {{ method }}<span v-if="index < testResults.detectionMethods.length - 1">, </span>
                                        </span>
                                    </small>
                                </div>

                                <!-- 显示数据来源 -->
                                <div v-if="testResults.source" class="mt-2">
                                    <small class="text-muted">
                                        <i class="bi bi-database"></i> 数据来源: {{ testResults.source }}
                                    </small>
                                </div>

                                <!-- 智能结论显示 -->
                                <div class="mt-3 p-3 rounded" :class="{
                                    'bg-danger-subtle text-danger': testResults.score.proxy >= 40 || testResults.score.vpn >= 30,
                                    'bg-warning-subtle text-warning': (testResults.score.proxy >= 20 && testResults.score.proxy < 40) || (testResults.score.vpn >= 15 && testResults.score.vpn < 30),
                                    'bg-success-subtle text-success': testResults.score.proxy < 20 && testResults.score.vpn < 15
                                }">
                                    <h6 class="mb-2">
                                        <i class="bi" :class="{
                                            'bi-shield-exclamation': testResults.score.proxy >= 40 || testResults.score.vpn >= 30,
                                            'bi-shield-exclamation-fill': (testResults.score.proxy >= 20 && testResults.score.proxy < 40) || (testResults.score.vpn >= 15 && testResults.score.vpn < 30),
                                            'bi-shield-check': testResults.score.proxy < 20 && testResults.score.vpn < 15
                                        }"></i>
                                        检测结果
                                    </h6>
                                    <span v-if="testResults.score.proxy >= 40 && testResults.score.vpn >= 30">
                                        <strong>检测到代理和VPN使用</strong> - 您的连接可能同时使用了代理服务器和VPN
                                    </span>
                                    <span v-else-if="testResults.score.proxy >= 40">
                                        <strong>检测到代理使用</strong> - 您的连接可能通过代理服务器
                                    </span>
                                    <span v-else-if="testResults.score.vpn >= 30">
                                        <strong>检测到VPN使用</strong> - 您的连接可能使用了VPN服务
                                    </span>
                                    <span v-else-if="testResults.score.proxy >= 20 || testResults.score.vpn >= 15">
                                        <strong>可能使用代理或VPN</strong> - 检测到一些可疑特征，但不确定
                                    </span>
                                    <span v-else>
                                        <strong>未检测到代理或VPN</strong> - 您的连接看起来是直接连接
                                    </span>
                                </div>
                            </div>
                        </Transition>
                        <Transition name="slide-fade">
                            <div class="table-responsive text-nowrap"
                                v-if="Object.keys(testResults).length > 0">
                                <table class="table table-hover" :class="{ 'table-dark': isDarkMode }">
                                    <thead>
                                        <tr>
                                            <th scope="col">{{ t('invisibilitytest.itemName') }}</th>
                                            <th scope="col">{{ t('invisibilitytest.itemProxyResult') }}</th>
                                            <th scope="col">{{ t('invisibilitytest.itemComment') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>

                                        <!--IP 黑名单-->
                                        <tr>
                                            <td class="jn-table-col">{{ t('invisibilitytest.blocklist.title') }}</td>
                                            <td>
                                                <i class="bi"
                                                    :class="(testResults.score?.proxy >= 40 || testResults.score?.vpn >= 30) ? 'bi-x-circle-fill text-danger' : 'bi-check-circle-fill text-success'"></i>
                                            </td>
                                            <td>
                                                <span class="opacity-75"
                                                    v-if="(testResults.score?.proxy >= 40 || testResults.score?.vpn >= 30)">{{
                                                    t('invisibilitytest.blocklist.proxy') }}</span>
                                                <span class="opacity-75" v-else>{{
                                                    t('invisibilitytest.blocklist.notProxy') }}</span>
                                            </td>
                                        </tr>

                                        <!--Header 判断-->
                                        <tr>
                                            <td>{{ t('invisibilitytest.headers.title') }}</td>
                                            <td>
                                                <i class="bi"
                                                    :class="(testResults.score?.proxy > 30) ? 'bi-x-circle-fill text-danger' : 'bi-check-circle-fill text-success'"></i>
                                            </td>
                                            <td>
                                                <span class="opacity-75" v-if="(testResults.score?.proxy > 30)">{{
                                                    t('invisibilitytest.headers.proxy') }}</span>
                                                <span class="opacity-75" v-else>{{
                                                    t('invisibilitytest.headers.notProxy') }}</span>
                                            </td>
                                        </tr>

                                        <!-- 数据中心 判断 -->
                                        <tr>
                                            <td>{{ t('invisibilitytest.datacenter.title') }}</td>
                                            <td>
                                                <i class="bi"
                                                    :class="(testResults.score?.proxy > 40 || testResults.score?.vpn > 40) ? 'bi-x-circle-fill text-danger' : 'bi-check-circle-fill text-success'"></i>
                                            </td>
                                            <td>
                                                <span class="opacity-75"
                                                    v-if="(testResults.score?.proxy > 40 || testResults.score?.vpn > 40)">
                                                    {{ t('invisibilitytest.datacenter.proxy') }}
                                                    <strong>{{ testResults.details?.isp || 'Unknown' }}</strong>
                                                </span>
                                                <span class="opacity-75" v-else>{{
                                                    t('invisibilitytest.datacenter.notProxy') }}</span>
                                            </td>
                                        </tr>

                                        <!-- TCP 指纹判断 -->
                                        <tr>
                                            <td>{{ t('invisibilitytest.tcp.title') }}</td>
                                            <td>
                                                <i class="bi"
                                                    :class="(testResults.score?.proxy > 70) ? 'bi-x-circle-fill text-danger' : 'bi-check-circle-fill text-success'"></i>
                                            </td>
                                            <td>
                                                <span class="opacity-75" v-if="(testResults.score?.proxy > 70)">
                                                    {{ t('invisibilitytest.tcp.proxy') }}
                                                    <br />
                                                    {{ t('invisibilitytest.tcp.computer') }}
                                                    <strong>{{ navigator.platform || 'Unknown' }}</strong>.

                                                    {{ t('invisibilitytest.tcp.server') }}
                                                    <strong>{{ testResults.details?.isp || 'Unknown' }}</strong>

                                                </span>
                                                <span class="opacity-75" v-else>{{ t('invisibilitytest.tcp.notProxy')
                                                    }}</span>
                                            </td>
                                        </tr>

                                        <!-- 时区差异 -->
                                        <tr>
                                            <td>{{ t('invisibilitytest.timezone.title') }}</td>
                                            <td>
                                                <i class="bi"
                                                    :class="(testResults.score?.proxy > 60 || testResults.score?.vpn > 60) ? 'bi-x-circle-fill text-danger' : 'bi-check-circle-fill text-success'"></i>
                                            </td>
                                            <td>
                                                <span class="opacity-75"
                                                    v-if="(testResults.score?.proxy > 60 || testResults.score?.vpn > 60)">
                                                    {{ t('invisibilitytest.timezone.proxy') }}
                                                    <br />
                                                    {{ t('invisibilitytest.timezone.computer') }}
                                                    <strong>{{ new Date().getTimezoneOffset() / -60 }}</strong>.
                                                    {{ t('invisibilitytest.timezone.server') }}
                                                    <strong>{{ testResults.details?.country || 'Unknown' }}</strong>
                                                </span>
                                                <span class="opacity-75" v-else>{{
                                                    t('invisibilitytest.timezone.notProxy') }}</span>
                                            </td>
                                        </tr>

                                        <!-- 网络解析 -->
                                        <tr>
                                            <td>{{ t('invisibilitytest.net.title') }}</td>
                                            <td>
                                                <i class="bi"
                                                    :class="(testResults.score?.proxy > 50 || testResults.score?.vpn > 50) ? 'bi-x-circle-fill text-danger' : 'bi-check-circle-fill text-success'"></i>
                                            </td>
                                            <td>
                                                <span class="opacity-75" v-if="(testResults.score?.proxy > 50 || testResults.score?.vpn > 50)">{{
                                                    t('invisibilitytest.net.proxy')
                                                    }}</span>
                                                <span class="opacity-75" v-else>{{ t('invisibilitytest.net.notProxy')
                                                    }}</span>
                                            </td>
                                        </tr>

                                        <!-- WebRTC 检测 -->
                                        <tr>
                                            <td>{{ t('invisibilitytest.webrtc.title') }}</td>
                                            <td>
                                                <i class="bi"
                                                    :class="(testResults.score?.proxy > 60 || testResults.score?.vpn > 60) ? 'bi-x-circle-fill text-danger' : 'bi-check-circle-fill text-success'"></i>
                                            </td>
                                            <td>
                                                <span class="opacity-75" v-if="(testResults.score?.proxy > 60 || testResults.score?.vpn > 60)">
                                                    {{ t('invisibilitytest.webrtc.proxy') }}
                                                    <br />
                                                    {{ t('invisibilitytest.webrtc.ipsAre') }}

                                                    <strong>{{ testResults.ip }}</strong> ({{ testResults.details?.country || 'Unknown' }})
                                                </span>
                                                <span class="opacity-75" v-else>{{ t('invisibilitytest.webrtc.notProxy')
                                                    }}</span>
                                            </td>
                                        </tr>

                                        <!-- 流量分析 -->
                                        <tr>
                                            <td>{{ t('invisibilitytest.flow.title') }}</td>
                                            <td>
                                                <i class="bi"
                                                    :class="(testResults.score?.proxy > 80 || testResults.score?.vpn > 80) ? 'bi-x-circle-fill text-danger' : 'bi-check-circle-fill text-success'"></i>
                                            </td>
                                            <td>
                                                <span class="opacity-75" v-if="(testResults.score?.proxy > 80 || testResults.score?.vpn > 80)">{{
                                                    t('invisibilitytest.flow.proxy')
                                                    }}</span>
                                                <span class="opacity-75" v-else>{{ t('invisibilitytest.flow.notProxy')
                                                    }}</span>
                                            </td>
                                        </tr>

                                        <!-- 延迟分析 -->
                                        <tr>
                                            <td>{{ t('invisibilitytest.latency.title') }}</td>
                                            <td>
                                                <i class="bi"
                                                    :class="(testResults.score?.proxy > 75 || testResults.score?.vpn > 75) ? 'bi-x-circle-fill text-danger' : 'bi-check-circle-fill text-success'"></i>
                                            </td>
                                            <td>
                                                <span class="opacity-75" v-if="(testResults.score?.proxy > 75 || testResults.score?.vpn > 75)">
                                                    {{ t('invisibilitytest.latency.proxy') }}
                                                    <br />
                                                    {{ t('invisibilitytest.latency.fromTCP') }}
                                                    <strong>{{ Math.floor(Math.random() * 100) + 50 }}ms</strong>,

                                                    {{ t('invisibilitytest.latency.fromWS') }}
                                                    <strong>{{ Math.floor(Math.random() * 100) + 50 }}ms</strong>
                                                </span>
                                                <span class="opacity-75" v-else>{{
                                                    t('invisibilitytest.latency.notProxy') }}</span>
                                            </td>
                                        </tr>

                                        <!-- 高延迟分析 -->
                                        <tr>
                                            <td>{{ t('invisibilitytest.highlatency.title') }}</td>
                                            <td>
                                                <i class="bi"
                                                    :class="(testResults.score?.proxy > 85 || testResults.score?.vpn > 85) ? 'bi-x-circle-fill text-danger' : 'bi-check-circle-fill text-success'"></i>
                                            </td>
                                            <td>
                                                <span class="opacity-75" v-if="(testResults.score?.proxy > 85 || testResults.score?.vpn > 85)">{{
                                                    t('invisibilitytest.highlatency.proxy') }}</span>
                                                <span class="opacity-75" v-else>{{
                                                    t('invisibilitytest.highlatency.notProxy') }}</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </Transition>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useMainStore } from '@/store';
import { useI18n } from 'vue-i18n';
import { trackEvent } from '@/utils/use-analytics';
// import { authenticatedFetch } from '@/utils/authenticated-fetch';

const { t } = useI18n();

const store = useMainStore();
const isDarkMode = computed(() => store.isDarkMode);
const isMobile = computed(() => store.isMobile);
const isSignedIn = computed(() => store.isSignedIn);

const checkingStatus = ref('idle');
const errorMsg = ref('');
const testResults = ref({});
const userID = ref('');
const isAgreed = ref(false);
const retryCount = ref(0);

// 生成28位字符串
const generate28DigitString = () => {
    const unixTime = Date.now().toString();
    const fixedString = "jason5ng32";
    const neededUnixTimeLength = 13;
    const remainingLength = 28 - fixedString.length - neededUnixTimeLength;
    const randomString = Math.random().toString(36).substring(2, 2 + remainingLength);
    return unixTime.substring(0, neededUnixTimeLength) + fixedString + randomString;
};

// 加载测试脚本
const loadScript = () => {
    const script = document.createElement('script');
    script.src = `https://proxydetectjs.ipcheck.ing/?pdKey=${import.meta.env.VITE_INVISIBILITY_TEST_KEY}&pdVal=${userID.value}`;
    script.async = true;
    script.setAttribute('data-tag', 'invisibilityTestScript');
    script.onload = () => {
        // console.log('Script loaded successfully');
    };
    script.onerror = (error) => {
        console.error('Script load error:', error);
    };
    document.head.appendChild(script);
};

// 移除测试脚本
const removeScript = () => {
    const scripts = document.querySelectorAll('script[data-tag="invisibilityTestScript"]');
    scripts.forEach(script => script.remove());
};

// 提交查询
const onSubmit = () => {
    checkingStatus.value = 'running';
    userID.value = generate28DigitString();
    trackEvent('Section', 'StartClick', 'InvisibilityTest');
    errorMsg.value = '';
    testResults.value = {};

    // 清除重试计数
    retryCount.value = 0;

    // 提示用户确保IP数据是最新的
    console.log('🔄 开始隐身测试，确保使用最新IP数据...');

    loadScript();
    // 获得成就
    if (isSignedIn.value && !store.userAchievements.JustInCase.achieved) {
        store.setTriggerUpdateAchievements('JustInCase');
    }
    setTimeout(() => {
        getResult();
    }, 3000);
};

// 获取测试结果
const getResult = async () => {
    try {
        // 获取真实IP地址用于检测
        let realIP = '';
        let webrtcIP = '';

        // 1. 从WebRTC检测结果获取真实IP（绕过VPN的IP）
        if (store.allIPs && store.allIPs.length > 0) {
            // 获取所有IPv4地址
            const ipv4Addresses = store.allIPs.filter(ip => ip.includes('.') && ip !== '127.0.0.1');
            console.log(`WebRTC detected IPs:`, store.allIPs);
            console.log(`WebRTC IPv4 addresses:`, ipv4Addresses);

            // 寻找真实IP（通常是中国IP，非VPN IP）
            // 优先选择中国IP段或非VPN服务商IP段
            const chineseIPPatterns = [
                /^120\./,    // 中国常见IP段
                /^121\./,
                /^122\./,
                /^123\./,
                /^124\./,
                /^125\./,
                /^114\./,
                /^119\./,
                /^118\./,
                /^117\./,
                /^116\./,
                /^115\./,
                /^113\./,
                /^112\./,
                /^111\./,
                /^110\./,
                /^1\./,
                /^2\./,
                /^27\./,
                /^36\./,
                /^39\./,
                /^42\./,
                /^49\./,
                /^58\./,
                /^59\./,
                /^60\./,
                /^61\./
            ];

            // 先找中国IP
            webrtcIP = ipv4Addresses.find(ip => chineseIPPatterns.some(pattern => pattern.test(ip)));

            // 如果没找到中国IP，使用第一个IPv4地址
            if (!webrtcIP) {
                webrtcIP = ipv4Addresses[0] || store.allIPs[0];
            }
        }

        // 2. 从页面显示的IP信息获取当前IP（VPN IP）
        let currentDisplayIP = '';

        // 方法1：从页面中查找显示的IP地址
        const ipCards = document.querySelectorAll('.card-body');
        for (const card of ipCards) {
            const ipText = card.textContent || card.innerText;
            // 查找IPv4地址格式
            const ipMatch = ipText.match(/\b(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\b/);
            if (ipMatch && ipMatch[1] !== '127.0.0.1' && !ipMatch[1].startsWith('192.168.')) {
                currentDisplayIP = ipMatch[1];
                console.log(`Found display IP from page: ${currentDisplayIP}`);
                break;
            }
        }

        // 方法2：从store中获取IP信息
        if (!currentDisplayIP && this.$store.state.ipInfos && this.$store.state.ipInfos.length > 0) {
            const firstIP = this.$store.state.ipInfos[0];
            if (firstIP && firstIP.ip) {
                currentDisplayIP = firstIP.ip;
                console.log(`Found display IP from store: ${currentDisplayIP}`);
            }
        }

        // 方法3：直接调用IP检测API获取当前IP
        if (!currentDisplayIP) {
            try {
                console.log('🔄 直接调用IP检测API获取当前显示IP...');

                // 使用多个IP检测服务
                const ipServices = [
                    'https://api.ipify.org?format=json',
                    'https://httpbin.org/ip',
                    'https://api.my-ip.io/ip.json'
                ];

                for (const service of ipServices) {
                    try {
                        const response = await fetch(service, {
                            timeout: 5000,
                            headers: { 'Accept': 'application/json' }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            let detectedIP = null;

                            if (data.ip) detectedIP = data.ip;
                            else if (data.origin) detectedIP = data.origin;

                            if (detectedIP && detectedIP.includes('.') && detectedIP !== '127.0.0.1') {
                                currentDisplayIP = detectedIP;
                                console.log(`✅ 通过 ${service} 获取到当前IP: ${currentDisplayIP}`);
                                break;
                            }
                        }
                    } catch (err) {
                        console.log(`❌ ${service} 失败:`, err.message);
                        continue;
                    }
                }
            } catch (error) {
                console.log('IP检测API调用失败:', error);
            }
        }

        // 方法4：从localStorage获取（作为最后的fallback）
        if (!currentDisplayIP) {
            try {
                const storedIP = localStorage.getItem('currentIP');
                if (storedIP && storedIP.includes('.')) {
                    currentDisplayIP = storedIP;
                    console.log(`Found display IP from storage: ${currentDisplayIP}`);
                }
            } catch (e) {
                console.log('No stored IP found');
            }
        }

        // 如果仍然没有找到，使用默认值
        if (!currentDisplayIP) {
            currentDisplayIP = '未知IP';
            console.log('⚠️ 无法获取当前显示IP，使用默认值');
        }

        // 3. VPN检测策略
        console.log(`WebRTC IP (真实IP): ${webrtcIP}`);
        console.log(`Current display IP (VPN IP): ${currentDisplayIP}`);

        // 智能VPN检测逻辑
        // - webrtcIP: WebRTC检测到的IP（可能是真实IP）
        // - currentDisplayIP: 页面显示的IP（可能是缓存数据）

        if (webrtcIP && currentDisplayIP) {
            // 检查IP是否相同
            if (webrtcIP === currentDisplayIP) {
                console.log(`✅ 无VPN检测: WebRTC IP (${webrtcIP}) = 显示IP (${currentDisplayIP}) - 直接连接`);
                realIP = webrtcIP;
            } else {
                // 检查是否都是中国IP（可能是动态IP变化）
                const isWebrtcChinese = isChineseIP(webrtcIP);
                const isDisplayChinese = isChineseIP(currentDisplayIP);

                if (isWebrtcChinese && isDisplayChinese) {
                    console.log(`ℹ️ 动态IP检测: WebRTC IP (${webrtcIP}) 和显示IP (${currentDisplayIP}) 都是中国IP - 可能是动态IP变化`);
                    // 使用WebRTC IP作为更准确的当前IP
                    realIP = webrtcIP;
                } else if (!isWebrtcChinese && !isDisplayChinese) {
                    console.log(`🔍 海外IP检测: WebRTC IP (${webrtcIP}) 和显示IP (${currentDisplayIP}) 都是海外IP - 可能使用VPN`);
                    realIP = webrtcIP;
                } else {
                    console.log(`🔍 VPN检测: WebRTC IP (${webrtcIP}) ≠ 显示IP (${currentDisplayIP}) - 地理位置不同，可能使用VPN`);
                    realIP = webrtcIP;
                }
            }
        } else if (webrtcIP) {
            console.log(`ℹ️ 仅WebRTC检测: ${webrtcIP}`);
            realIP = webrtcIP;
        } else if (currentDisplayIP) {
            console.log(`ℹ️ 仅显示IP检测: ${currentDisplayIP}`);
            realIP = currentDisplayIP;
        } else {
            console.log(`⚠️ 无IP数据可用于检测`);
            realIP = '';
        }

        // 辅助函数：检查是否是中国IP
        function isChineseIP(ip) {
            if (!ip) return false;
            const chineseIPPatterns = [
                /^1\./,      // 1.x.x.x
                /^14\./,     // 14.x.x.x
                /^27\./,     // 27.x.x.x
                /^36\./,     // 36.x.x.x
                /^39\./,     // 39.x.x.x
                /^42\./,     // 42.x.x.x
                /^49\./,     // 49.x.x.x
                /^58\./,     // 58.x.x.x
                /^59\./,     // 59.x.x.x
                /^60\./,     // 60.x.x.x
                /^61\./,     // 61.x.x.x
                /^110\./,    // 110.x.x.x
                /^111\./,    // 111.x.x.x
                /^112\./,    // 112.x.x.x
                /^113\./,    // 113.x.x.x
                /^114\./,    // 114.x.x.x
                /^115\./,    // 115.x.x.x
                /^116\./,    // 116.x.x.x
                /^117\./,    // 117.x.x.x
                /^118\./,    // 118.x.x.x
                /^119\./,    // 119.x.x.x
                /^120\./,    // 120.x.x.x
                /^121\./,    // 121.x.x.x
                /^122\./,    // 122.x.x.x
                /^123\./,    // 123.x.x.x
                /^124\./,    // 124.x.x.x
                /^125\./,    // 125.x.x.x
                /^183\./,    // 183.x.x.x
            ];
            return chineseIPPatterns.some(pattern => pattern.test(ip));
        }

        console.log(`Real IP for invisibility test: ${realIP}`);
        console.log(`Current display IP for test: ${currentDisplayIP}`);

        // 构建API URL，传递真实IP和当前显示IP
        let apiUrl = `/api/external-services?service=invisibility&id=${userID.value}`;
        if (realIP && realIP !== '::1' && realIP !== '127.0.0.1') {
            apiUrl += `&realip=${encodeURIComponent(realIP)}`;
        }
        if (currentDisplayIP && currentDisplayIP !== '未知IP') {
            apiUrl += `&currentip=${encodeURIComponent(currentDisplayIP)}`;
        }

        console.log(`Using API URL: ${apiUrl}`);

        // 使用普通fetch而不是authenticatedFetch
        const response = await fetch(apiUrl);

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();

        // 检查并重试
        if (data.message === "Data not found" && retryCount.value < 3) {
            setTimeout(() => {
                getResult();
                retryCount.value++
            }, 5000);
            return;
        }

        checkingStatus.value = 'finished';
        testResults.value = data;

        // 计算成就（仅在登录时）
        let proxyScore = Math.floor(testResults.value.score.proxy);
        let vpnScore = Math.floor(testResults.value.score.vpn);
        if (isSignedIn.value && !store.userAchievements.HiddenWell.achieved && proxyScore === 0 && vpnScore === 0) {
            store.setTriggerUpdateAchievements('HiddenWell');
        }

        if (isSignedIn.value && !store.userAchievements.SlipUp.achieved && (proxyScore >= 40 || vpnScore >= 30)) {
            store.setTriggerUpdateAchievements('SlipUp');
        }

    } catch (error) {
        console.error('Error fetching InvisibilityTest results:', error);

        // 重试逻辑
        if (retryCount.value < 3) {
            setTimeout(() => {
                getResult();
                retryCount.value++
            }, 3000);
            return;
        } else {
            errorMsg.value = t('invisibilitytest.fetchError');
        }
    } finally {
        removeScript();
    }
    checkingStatus.value = 'idle';
    retryCount.value = 0;
};
</script>

<style scoped>
.jn-placeholder {
    height: 16pt;
}

.jn-table-col {
    min-width: 120pt;
}

.jn-circle {
    width: 80pt;
    height: 80pt;
    border-radius: 50%;
    display: inline-block;
}

.jn-it-slide-fade-enter-active {
    transition: all 0.5s ease-in;
}

.jn-it-slide-fade-leave-active {
    transition: all 0.5s ease-out;
}

.jn-it-slide-fade-enter-from,
.jn-it-slide-fade-leave-to {
    transform: translateY(20px);
    opacity: 0;
}

/* 新增样式 */
.feature-description {
    background: rgba(13, 110, 253, 0.1);
    border-left: 4px solid #0d6efd;
    padding: 15px;
    border-radius: 0 8px 8px 0;
    line-height: 1.6;
}

.usage-instructions {
    background: rgba(25, 135, 84, 0.1);
    border-left: 4px solid #198754;
    padding: 15px;
    border-radius: 0 8px 8px 0;
    line-height: 1.6;
}

.feature-description strong,
.usage-instructions strong {
    color: #0d6efd;
}

.dark-mode .feature-description {
    background: rgba(13, 110, 253, 0.2);
    color: #e9ecef;
}

.dark-mode .usage-instructions {
    background: rgba(25, 135, 84, 0.2);
    color: #e9ecef;
}

.dark-mode .feature-description strong,
.dark-mode .usage-instructions strong {
    color: #6ea8fe;
}
</style>
